import AppError from "../middlewares/AppError.js";

/**
 * Generates a random string with specified length and character set
 * @param {number} length - Length of the string to generate
 * @param {string} characters - Character set to use
 * @returns {string} Generated random string
 */
export function generateRandomString(length, characters) {
  let result = "";
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

/**
 * Generates a 9-digit numeric transaction ID for QwikCilver
 * @returns {string} 9-digit transaction ID
 */
export function generateStringTransactionId() {
  return generateRandomString(9, "0123456789");
}

/**
 * Generates a 15-character alphanumeric idempotency key
 * @returns {string} 15-character idempotency key
 */
export function generateIdempotencyKey() {
  return generateRandomString(15, "abcdefghijklmnopqrstuvwxyz0123456789");
}

/**
 * Extracts and validates authorization token from headers
 * @param {Object} headers - Request headers
 * @returns {string} Clean authorization token
 */
export function extractAuthToken(headers) {
  const token = headers.authorization?.replace("Bearer ", "");
  if (!token) {
    throw new AppError("Missing authorization token", 401, {
      errors: [{ field: "headers", message: "Authorization header is required" }]
    });
  }
  return token;
}

/**
 * Validates required QwikCilver headers
 * @param {Object} headers - Request headers
 * @returns {Object} Validated header values
 */
export function validateQwikCilverHeaders(headers) {
  const authToken = extractAuthToken(headers);
  const transactionId = headers.transactionid;
  const dateAtClient = headers.dateatclient;

  if (!transactionId || !dateAtClient) {
    throw new AppError("Missing required headers", 400, {
      errors: [
        { field: "headers", message: "TransactionId and DateAtClient headers are required" }
      ]
    });
  }

  return { authToken, transactionId, dateAtClient };
}

/**
 * Validates card data from request body
 * @param {Object} body - Request body
 * @param {Array} requiredFields - Required card fields
 * @returns {Object} Validated card data
 */
export function validateCardData(body, requiredFields = ['CardNumber', 'CardPIN']) {
  // Check if card data is provided directly in body (new simplified structure)
  const missingFields = requiredFields.filter(field => !body[field]);

  if (missingFields.length > 0) {
    throw new AppError("Missing card fields", 400, {
      errors: [
        {
          field: "body",
          message: `Missing required fields: ${missingFields.join(', ')}`
        }
      ]
    });
  }

  // Return the card data directly from body
  const cardData = {};
  requiredFields.forEach(field => {
    cardData[field] = body[field];
  });

  return cardData;
}

/**
 * Creates standardized error response for API failures
 * @param {string} message - Error message
 * @param {Error} error - Original error object
 * @param {string} field - Field name for error context
 * @returns {AppError} Formatted error
 */
export function createApiError(message, error, field = "api") {
  const errRes = error.response?.data || error;
  return new AppError(message, error.response?.status || 500, {
    errors: [
      {
        field,
        message: errRes.message || JSON.stringify(errRes),
      },
    ],
  });
}

/**
 * Creates QwikCilver API headers
 * @param {string} authToken - Authorization token
 * @param {string} transactionId - Transaction ID
 * @param {string} dateAtClient - Date at client (optional, defaults to current time)
 * @returns {Object} Headers object
 */
export function createQwikCilverHeaders(authToken, transactionId, dateAtClient = null) {
  return {
    'Authorization': `Bearer ${authToken}`,
    'DateAtClient': dateAtClient || new Date().toISOString(),
    'TransactionId': transactionId,
    'Content-Type': 'application/json',
  };
}

/**
 * Creates Rewardify API headers
 * @param {string} token - Rewardify token
 * @returns {Object} Headers object
 */
export function createRewardifyHeaders(token) {
  return {
    'accept': 'application/json',
    'authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
}

/**
 * Validates and formats Rewardify credit request data
 * @param {Object} data - Request data
 * @returns {Object} Validated and formatted data
 */
export function validateRewardifyData(data) {
  const { email, amount, memo, sendEmail, emailNote, expiresAt } = data;
  
  const requiredFields = { email, amount, memo, emailNote };
  const missingFields = Object.entries(requiredFields)
    .filter(([key, value]) => !value)
    .map(([key]) => key);

  if (missingFields.length > 0 || sendEmail === undefined) {
    throw new AppError("Missing required fields", 400, {
      errors: [
        { 
          field: "body", 
          message: `Missing required fields: ${missingFields.join(', ')}${sendEmail === undefined ? ', sendEmail' : ''}` 
        }
      ]
    });
  }

  const payload = {
    email: email.trim(),
    amount: amount,
    memo: memo.trim(),
    sendEmail: Boolean(sendEmail),
    emailNote: emailNote.trim()
  };

  if (expiresAt && expiresAt !== '') {
    payload.expiresAt = expiresAt;
  }

  return payload;
}

/**
 * Generates invoice number and idempotency key
 * @returns {Object} Invoice number and idempotency key
 */
export function generateInvoiceData() {
  const invoiceNumber = `INV-${Date.now()}`;
  const idempotencyKey = `${invoiceNumber}-${generateIdempotencyKey()}`;
  return { invoiceNumber, idempotencyKey };
}

/**
 * Creates QwikCilver redeem payload
 * @param {Object} params - Parameters for redeem payload
 * @returns {Object} Redeem payload
 */
export function createRedeemPayload({ invoiceNumber, idempotencyKey, cardData, notes }) {
  return {
    TransactionTypeId: 302,
    InputType: 1,
    InvoiceNumber: invoiceNumber,
    IdempotencyKey: idempotencyKey,
    Cards: [cardData],
    Notes: notes
  };
}

/**
 * Creates notes string for QwikCilver redeem
 * @param {Object} params - Parameters for notes
 * @returns {string} Formatted notes string
 */
export function createRedeemNotes({ amount, invoiceAmount, invoiceNumber }) {
  return `{VldType~GCRDM|AMT~${amount}|BillAmount~${invoiceAmount}|InvoiceNumber~${invoiceNumber}}`;
}
