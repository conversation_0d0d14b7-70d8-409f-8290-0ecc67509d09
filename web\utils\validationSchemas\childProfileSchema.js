import <PERSON><PERSON> from "joi";

// Schema for individual profile object
const profileSchema = Joi.object({
  id: Joi.string().required(),
  name: Joi.string().required(),
  dob: Joi.date().iso().required(),
  relationship: Joi.string().required(),
});

// Schema for the data object
const dataSchema = Joi.object({
  customerId: Joi.string().required(),
  profiles: Joi.array().items(profileSchema).min(1).max(5).required(),
});

// Schema for form data validation
export const childProfileFormSchema = Joi
  .object({
    // Optional image fields for child_1 through child_5
    image_child_1: Joi.any().optional(),
    image_child_2: Joi.any().optional(),
    image_child_3: Joi.any().optional(),
    image_child_4: Joi.any().optional(),
    image_child_5: Joi.any().optional(),

    // Data object containing customerId and profiles
    data: dataSchema.required(),
  })
  .custom((value, helpers) => {
    // Custom validation to ensure each profile has a corresponding image
    const { data, ...images } = value;

    if (!data || !data.profiles) {
      return helpers.error("any.invalid", {
        message: "Invalid data structure",
      });
    }

    for (const profile of data.profiles) {
      const imageKey = `image_${profile.id}`;
      if (!images[imageKey] || !images[imageKey][0]) {
        return helpers.error("any.invalid", {
          message: `Image for profile ${profile.id} is required`,
          field: imageKey,
          path: [imageKey],
        });
      }
    }

    return value;
  });
