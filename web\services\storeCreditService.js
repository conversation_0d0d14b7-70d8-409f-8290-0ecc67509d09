import dotenv from 'dotenv';
import { qwikCilverClient, rewardifyClient } from '../utils/apiClient.js';
import { createQwikCilverHeaders, createRewardifyHeaders, formattedDate, validateRewardifyData } from '../utils/storeCreditHelpers.js';

dotenv.config();

export async function authorizeQwikCilver(transactionId) {
  const payload = {
    TransactionId: transactionId,
    UserName: process.env.QWIKCILVER_USERNAME,
    Password: process.env.QWIKCILVER_PASSWORD,
    ForwardingEntityId: process.env.QWIKCILVER_ENTITY_ID,
    ForwardingEntityPassword: process.env.QWIKCILVER_ENTITY_PASSWORD,
    TerminalId: process.env.QWIKCILVER_TERMINAL_ID,
  };

  const headers = {
    'Content-Type': 'application/json',
    'charset': 'UTF-8',
    'DateAtClient': formattedDate(),
  };

  try {
    return await qwikCilverClient.post('/authorize', payload, headers);
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function getGiftCardBalance({ authToken, transactionId, cardNumber, cardPIN }) {
  const payload = {
    TransactionTypeId: "306",
    InputType: "1",
    Cards: [{ CardNumber: cardNumber, CardPIN: cardPIN }]
  };
  const headers = createQwikCilverHeaders(authToken, transactionId);
  try {
    return await qwikCilverClient.post('/gc/transactions', payload, headers);
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function redeemGiftCard({ authToken, transactionId, dateAtClient, payload }) {
  const headers = createQwikCilverHeaders(authToken, transactionId, dateAtClient);
  try {
    return await qwikCilverClient.post('/gc/transactions', payload, headers);
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}


export async function rewardifyAuth() {
  const payload = {
    grant_type: 'client_credentials',
    client_id: process.env.REWARDIFY_CLIENT_ID,
    client_secret: process.env.REWARDIFY_CLIENT_SECRET
  };
  const headers = {
    'accept': 'application/json',
    'Content-Type': 'application/json',
  };
  try {
    return await rewardifyClient.post('/oauth/v2/token', payload, headers);
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function checkRewardifyCustomer(rewardifyToken, customerId) {
  const headers = createRewardifyHeaders(rewardifyToken);
  try {
    return await rewardifyClient.get(`/customer/${customerId}`, headers);
  } catch (error) {
    throw error;
  }
}

export async function creditRewardifyStoreCredit({ rewardifyToken, customerId, email, amount, memo, expiresAt, sendEmail, emailNote }) {
  const payload = validateRewardifyData({ email, amount, memo, expiresAt, sendEmail, emailNote });
  const headers = createRewardifyHeaders(rewardifyToken);

  try {
    return await rewardifyClient.put(`/customer/${customerId}/account/credit`, payload, headers);
  } catch (error) {
    let errorMessage = "Unknown Rewardify API error";
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.response?.data?.title) {
      errorMessage = error.response.data.title;
    } else if (error.response?.statusText) {
      errorMessage = `${error.response.status} ${error.response.statusText}`;
    }

    const enhancedError = new Error(`Rewardify API Error: ${errorMessage}`);
    enhancedError.response = error.response;
    enhancedError.status = error.response?.status;
    throw enhancedError;
  }
}
