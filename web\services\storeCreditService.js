import dotenv from "dotenv";
import {
  createQwikCilverHeaders,
  createRewardifyHeaders,
  formattedDate,
  validateRewardifyData,
} from "../utils/storeCreditHelpers.js";
import AppError from "../middlewares/AppError.js";
import { globalPost, globalGet, globalPut, buildRewardifyUrl, buildQwikCilverUrl } from "../utils/globalAxios.js";

dotenv.config();

export async function authorizeQwikCilver(transactionId) {
  if (!process.env.QWIKCILVER_API_BASE_URL) {
    throw new AppError(
      "QWIKCILVER_API_BASE_URL environment variable is not set",
      500,
      {
        errors: [
          {
            field: "QWIKCILVER_API_BASE_URL",
            message: "QWIKCILVER_API_BASE_URL environment variable is not set",
          },
        ],
      }
    );
  }

  const payload = {
    InputType: "1",
    UserName: process.env.QWIKCILVER_USERNAME,
    Password: process.env.QWIKCILVER_PASSWORD,
    ForwardingEntityId: process.env.QWIKCILVER_ENTITY_ID,
    ForwardingEntityPassword: process.env.QWIKCILVER_ENTITY_PASSWORD,
    TerminalId: process.env.QWIKCILVER_TERMINAL_ID,
  };

  const headers = {
    "Content-Type": "application/json",
    TransactionId: transactionId,
    DateAtClient: formattedDate(),
  };

  try {
    const url = buildQwikCilverUrl('authorize');
    return await globalPost(url, payload, { headers });
  } catch (error) {
    throw new AppError("QwikCilver authorize failed", 500, {
      errors: [
        {
          field: "authorizeQwikCilver",
          message: error.message || "Unknown error",
        },
      ],
    });
  }
}

export async function getGiftCardBalance({
  authToken,
  transactionId,
  cardNumber,
  cardPIN,
}) {
  const payload = {
    TransactionTypeId: "306",
    InputType: "1",
    Cards: [{ CardNumber: cardNumber, CardPIN: cardPIN }],
  };
  const headers = createQwikCilverHeaders(authToken, transactionId);

  try {
    const url = buildQwikCilverUrl('gc/transactions');
    return await globalPost(url, payload, { headers });
  } catch (error) {
    throw new AppError("Failed to get gift card balance", 500, {
      errors: [
        {
          field: "getGiftCardBalance",
          message: error.message || "Unknown error",
        },
      ],
    });
  }
}

export async function redeemGiftCard({
  authToken,
  transactionId,
  dateAtClient,
  payload,
}) {
  const baseUrl = process.env.QWIKCILVER_API_BASE_URL;
  const headers = createQwikCilverHeaders(
    authToken,
    transactionId,
    dateAtClient
  );
  try {
    return await axios.post(`${baseUrl}/gc/transactions`, payload, { headers });
  } catch (error) {
    throw new AppError("Failed to redeem gift card", 500, {
      errors: [
        {
          field: "redeemGiftCard",
          message: error.message || "Unknown error",
        },
      ],
    });
  }
}

export async function rewardifyAuth() {
  const baseUrl = process.env.REWARDIFY_API_BASE_URL;
  const payload = {
    grant_type: "client_credentials",
    client_id: process.env.REWARDIFY_CLIENT_ID,
    client_secret: process.env.REWARDIFY_CLIENT_SECRET,
  };
  const headers = {
    accept: "application/json",
    "Content-Type": "application/json",
  };
  try {
    return await axios.post(`${baseUrl}oauth/v2/token`, payload, { headers });
  } catch (error) {
    throw new AppError("Failed to authenticate with Rewardify", 500, {
      errors: [
        {
          field: "rewardifyAuth",
          message: error.message || "Unknown error",
        },
      ],
    });
  }
}

export async function checkRewardifyCustomer(rewardifyToken, customerId) {
  const baseUrl = process.env.REWARDIFY_API_BASE_URL;
  const headers = createRewardifyHeaders(rewardifyToken);
  try {
    return await axios.get(`${baseUrl}customer/${customerId}`, headers);
  } catch (error) {
    throw new AppError("Failed to check Rewardify customer", 500, {
      errors: [
        {
          field: "checkRewardifyCustomer",
          message: error.message || "Unknown error",
        },
      ],
    });
  }
}

export async function creditRewardifyStoreCredit({
  rewardifyToken,
  customerId,
  email,
  amount,
  memo,
  expiresAt,
  sendEmail,
  emailNote,
}) {
  const payload = validateRewardifyData({
    email,
    amount,
    memo,
    expiresAt,
    sendEmail,
    emailNote,
  });
  const headers = createRewardifyHeaders(rewardifyToken);
  const baseUrl = process.env.REWARDIFY_API_BASE_URL;

  try {
    return await axios.put(
      `${baseUrl}customer/${customerId}/account/credit`,
      payload,
      headers
    );
  } catch (error) {
    let errorMessage = "Unknown Rewardify API error";
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.response?.data?.title) {
      errorMessage = error.response.data.title;
    } else if (error.response?.statusText) {
      errorMessage = `${error.response.status} ${error.response.statusText}`;
    }
    throw new AppError("Failed to credit Rewardify store credit", 500, {
      errors: [
        {
          field: "creditRewardifyStoreCredit",
          message: errorMessage,
        },
      ],
    });
  }
}
