import dotenv from "dotenv";
import {
  createQwikCilverHeaders,
  createRewardifyHeaders,
  formattedDate,
  validateRewardifyData,
} from "../utils/storeCreditHelpers.js";
import axios from "axios";

dotenv.config();

export async function authorizeQwik<PERSON>ilver(transactionId) {
  const baseUrl = process.env.QWIKCILVER_API_BASE_URL;
  if (!process.env.QWIKCILVER_API_BASE_URL) {
    throw new Error("QWIKCILVER_API_BASE_URL environment variable is not set");
  }

  const payload = {
    InputType: "1",
    UserName: process.env.QWIKCILVER_USERNAME,
    Password: process.env.QWIKCILVER_PASSWORD,
    ForwardingEntityId: process.env.QWIKCILVER_ENTITY_ID,
    ForwardingEntityPassword: process.env.QWIKCILVER_ENTITY_PASSWORD,
    TerminalId: process.env.QWIKCILVER_TERMINAL_ID,
  };

  const headers = {
    "Content-Type": "application/json",
    TransactionId: transactionId,
    DateAtClient: formattedDate(),
  };

  try {
    return await axios.post(`${baseUrl}/authorize`, payload, { headers });
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function getGiftCardBalance({
  authToken,
  transactionId,
  cardNumber,
  cardPIN,
}) {
  const payload = {
    TransactionTypeId: "306",
    InputType: "1",
    Cards: [{ CardNumber: cardNumber, CardPIN: cardPIN }],
  };
  const headers = createQwikCilverHeaders(authToken, transactionId);

  const baseUrl = process.env.QWIKCILVER_API_BASE_URL;
  try {
    return await axios.post(`${baseUrl}gc/transactions`, payload, { headers });
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function redeemGiftCard({
  authToken,
  transactionId,
  dateAtClient,
  payload,
}) {
  const baseUrl = process.env.QWIKCILVER_API_BASE_URL;
  const headers = createQwikCilverHeaders(
    authToken,
    transactionId,
    dateAtClient
  );
  try {
    return await axios.post(`${baseUrl}/gc/transactions`, payload, { headers });
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function rewardifyAuth() {

  const baseUrl = process.env.REWARDIFY_API_BASE_URL;
  const payload = {
    grant_type: "client_credentials",
    client_id: process.env.REWARDIFY_CLIENT_ID,
    client_secret: process.env.REWARDIFY_CLIENT_SECRET,
  };
  const headers = {
    accept: "application/json",
    "Content-Type": "application/json",
  };
  try {
    return await axios.post(`${baseUrl}oauth/v2/token`, payload, { headers });
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function checkRewardifyCustomer(rewardifyToken, customerId) {
  const baseUrl = process.env.REWARDIFY_API_BASE_URL;
  const headers = createRewardifyHeaders(rewardifyToken);
  try {
    return await axios.get(`${baseUrl}customer/${customerId}`, headers);
  } catch (error) {
    throw error;
  }
}

export async function creditRewardifyStoreCredit({
  rewardifyToken,
  customerId,
  email,
  amount,
  memo,
  expiresAt,
  sendEmail,
  emailNote,
}) {
  const payload = validateRewardifyData({
    email,
    amount,
    memo,
    expiresAt,
    sendEmail,
    emailNote,
  });
  const headers = createRewardifyHeaders(rewardifyToken);
   const baseUrl = process.env.REWARDIFY_API_BASE_URL;

  try {
    return await axios.put(
      `${baseUrl}customer/${customerId}/account/credit`,
      payload,
      headers
    );
  } catch (error) {
    let errorMessage = "Unknown Rewardify API error";
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.response?.data?.title) {
      errorMessage = error.response.data.title;
    } else if (error.response?.statusText) {
      errorMessage = `${error.response.status} ${error.response.statusText}`;
    }

    const enhancedError = new Error(`Rewardify API Error: ${errorMessage}`);
    enhancedError.response = error.response;
    enhancedError.status = error.response?.status;
    throw enhancedError;
  }
}
