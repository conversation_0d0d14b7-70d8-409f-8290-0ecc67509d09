

import Joi from "joi";

export const cardSchema = Joi.object({
  CardNumber: Joi.string()
    .pattern(/^\d{16}$/)
    .required()
    .messages({
      "string.empty": "Card number is required",
      "string.pattern.base": "Card number must be 16 digits",
    }),
  CardPIN: Joi.string()
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      "string.empty": "Card PIN is required",
      "string.pattern.base": "Card PIN must be 4 digits",
    }),
});

export const redeemCardSchema = Joi.object({
  CardNumber: Joi.string()
    .pattern(/^\d{16}$/)
    .required()
    .messages({
      "string.empty": "Card number is required",
      "string.pattern.base": "Card number must be 16 digits",
    }),
  CardPIN: Joi.string()
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      "string.empty": "Card PIN is required",
      "string.pattern.base": "Card PIN must be 4 digits",
    }),
  Amount: Joi.number()
    .positive()
    .required()
    .messages({
      "number.base": "Amount must be a number",
      "number.positive": "Amount must be a positive number",
      "any.required": "Amount is required",
    }),
});
