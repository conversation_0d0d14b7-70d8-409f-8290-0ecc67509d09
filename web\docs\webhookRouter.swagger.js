/**
 * @swagger
 * tags:
 *   - name: Webhooks
 *     description: Shopify webhook handlers for store credit management
 */

/**
 * @swagger
 * /api/webhooks/refund:
 *   post:
 *     tags: [Webhooks]
 *     summary: Handle Shopify refund webhooks
 *     description: |
 *       Processes Shopify refund webhooks to automatically credit proportional store credit
 *       back to customers when they return items that were purchased using store credit.
 *       
 *       **Features:**
 *       - Calculates proportional store credit refunds based on returned items
 *       - Handles both COD and non-COD orders differently
 *       - For COD orders: Returns subtotal + tax + proportional store credit
 *       - For non-COD orders: Returns only proportional store credit amount
 *       - Automatically credits the calculated amount to customer's Rewardify account
 *       - Sends email notification to customer about the refund
 *       
 *       **Note:** This endpoint is called automatically by Shopify when refunds are processed.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: Shopify refund webhook payload
 *             properties:
 *               order_id:
 *                 type: integer
 *                 description: Shopify order ID
 *                 example: **************
 *               email:
 *                 type: string
 *                 description: Customer email
 *                 example: "<EMAIL>"
 *               refund_line_items:
 *                 type: array
 *                 description: Array of refunded line items
 *                 items:
 *                   type: object
 *                   properties:
 *                     quantity:
 *                       type: integer
 *                       description: Quantity being refunded
 *                       example: 1
 *                     subtotal:
 *                       type: number
 *                       description: Subtotal amount for refunded items
 *                       example: 999
 *                     total_tax:
 *                       type: number
 *                       description: Tax amount for refunded items
 *                       example: 179.82
 *                     line_item:
 *                       type: object
 *                       properties:
 *                         variant_id:
 *                           type: integer
 *                           description: Product variant ID
 *                           example: 55236414144881
 *                         title:
 *                           type: string
 *                           description: Product title
 *                           example: "Miniklub Boys Brown Lace Up Hard Sole Shoes"
 *               transactions:
 *                 type: array
 *                 description: Payment transactions for the refund
 *                 items:
 *                   type: object
 *                   properties:
 *                     gateway:
 *                       type: string
 *                       description: Payment gateway used
 *                       example: "bogus"
 *               order_adjustments:
 *                 type: array
 *                 description: Order adjustments (used to detect cancellations)
 *                 items:
 *                   type: object
 *                   properties:
 *                     kind:
 *                       type: string
 *                       example: "refund_discrepancy"
 *                     reason:
 *                       type: string
 *                       example: "Cancelled"
 *     responses:
 *       200:
 *         description: Refund webhook processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         refundAmount:
 *                           type: string
 *                           description: Amount credited to customer
 *                           example: "100.00"
 *                         transaction:
 *                           type: object
 *                           description: Rewardify transaction details
 *             examples:
 *               refund_processed:
 *                 summary: Store credit refund processed
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Refund webhook processed with store credit refund"
 *                   data:
 *                     refundAmount: "100.00"
 *                     transaction:
 *                       id: 9259871
 *                       amount: "100.00"
 *               no_credit_needed:
 *                 summary: No store credit refund needed
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Refund webhook processed - no store credit refund needed"
 *               cancellation_ignored:
 *                 summary: Cancellation ignored (handled by cancel webhook)
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Refund webhook ignored - cancellation already processed"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */

/**
 * @swagger
 * /api/webhooks/cancel:
 *   post:
 *     tags: [Webhooks]
 *     summary: Handle Shopify order cancellation webhooks
 *     description: |
 *       Processes Shopify order cancellation webhooks to automatically refund
 *       store credit that was used in the cancelled order.
 *       
 *       **Features:**
 *       - Refunds only the store credit amount that was used in the order
 *       - Does not refund other payment amounts (handled by Shopify)
 *       - Automatically credits the store credit back to customer's Rewardify account
 *       - Sends email notification to customer about the refund
 *       
 *       **Note:** This endpoint is called automatically by Shopify when orders are cancelled.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: Shopify order cancellation webhook payload
 *             properties:
 *               id:
 *                 type: integer
 *                 description: Shopify order ID
 *                 example: **************
 *               customer:
 *                 type: object
 *                 properties:
 *                   email:
 *                     type: string
 *                     description: Customer email
 *                     example: "<EMAIL>"
 *               cancelled_at:
 *                 type: string
 *                 format: date-time
 *                 description: Cancellation timestamp
 *                 example: "2025-07-04T10:30:00Z"
 *               cancel_reason:
 *                 type: string
 *                 description: Reason for cancellation
 *                 example: "customer"
 *     responses:
 *       200:
 *         description: Cancellation webhook processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         orderId:
 *                           type: integer
 *                           description: Order ID that was cancelled
 *                           example: **************
 *                         customerId:
 *                           type: string
 *                           description: Customer ID
 *                           example: "23476814184817"
 *                         refundAmount:
 *                           type: string
 *                           description: Store credit amount refunded
 *                           example: "200.00"
 *             examples:
 *               cancel_processed:
 *                 summary: Store credit refund processed for cancellation
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Cancel webhook processed with store credit refund"
 *                   data:
 *                     orderId: **************
 *                     customerId: "23476814184817"
 *                     refundAmount: "200.00"
 *               no_credit_needed:
 *                 summary: No store credit refund needed
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Cancel webhook processed - no store credit refund needed"
 *       400:
 *         description: Bad request - Missing order ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */
