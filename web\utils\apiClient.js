import axios from 'axios';

/**
 * Generic API client with standardized error handling and logging
 */
export class ApiClient {
  constructor(baseURL, defaultHeaders = {}) {
    this.baseURL = baseURL;
    this.defaultHeaders = defaultHeaders;
  }

  /**
   * Makes an HTTP request with standardized error handling
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {Object} data - Request data
   * @param {Object} headers - Request headers
   * @param {Object} options - Additional options
   * @returns {Promise} Response data
   */
  async request(method, url, data = null, headers = {}, options = {}) {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    const requestHeaders = { ...this.defaultHeaders, ...headers };
    
    const config = {
      method,
      url: fullUrl,
      headers: requestHeaders,
      ...options
    };

    if (data && ['post', 'put', 'patch'].includes(method.toLowerCase())) {
      config.data = data;
    }

    // Log request (with sensitive data redacted)
    this.logRequest(method, fullUrl, data, this.redactSensitiveHeaders(requestHeaders));

    try {
      const response = await axios(config);
      this.logSuccess(method, fullUrl, response.data);
      return response.data;
    } catch (error) {
      this.logError(method, fullUrl, error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get(url, headers = {}, options = {}) {
    return this.request('GET', url, null, headers, options);
  }

  /**
   * POST request
   */
  async post(url, data, headers = {}, options = {}) {
    return this.request('POST', url, data, headers, options);
  }

  /**
   * PUT request
   */
  async put(url, data, headers = {}, options = {}) {
    return this.request('PUT', url, data, headers, options);
  }

  /**
   * DELETE request
   */
  async delete(url, headers = {}, options = {}) {
    return this.request('DELETE', url, null, headers, options);
  }

  /**
   * Redacts sensitive information from headers for logging
   * @param {Object} headers - Headers object
   * @returns {Object} Headers with sensitive data redacted
   */
  redactSensitiveHeaders(headers) {
    const redacted = { ...headers };
    if (redacted.authorization) {
      redacted.authorization = `Bearer ${redacted.authorization.substring(7, 17)}...`;
    }
    if (redacted.Authorization) {
      redacted.Authorization = `Bearer ${redacted.Authorization.substring(7, 17)}...`;
    }
    return redacted;
  }

  /**
   * Logs API request
   */
  logRequest(method, url, data, headers) {
    console.log(`[API Request] ${method} ${url}`, {
      headers,
      ...(data && { data })
    });
  }

  /**
   * Logs successful API response
   */
  logSuccess(method, url, data) {
    console.log(`[API Success] ${method} ${url}`, data);
  }

  /**
   * Logs API error
   */
  logError(method, url, error) {
    console.error(`[API Error] ${method} ${url}`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });
  }
}

// Pre-configured API clients
export const qwikCilverClient = new ApiClient('https://ajf2m0r1na8eau2bn0brcou5h2i0-custuatdev.qwikcilver.com/QwikCilver/XnP/api/v3');
export const rewardifyClient = new ApiClient('https://api.rewardify.ca');
