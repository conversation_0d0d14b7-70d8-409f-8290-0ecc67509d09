import {
  updateChildProfilesService,
  getChildProfilesService,
} from "../services/kidsService.js";
import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { getATFromSQL } from "../middlewares/helpers.js";
import multer from "multer";

export const getChildProfiles = catchAsync(async (req, res) => {
  const storeData = await getATFromSQL();
  const shop = process.env.SHOP;

  const { customerId } = req.params;

  if (!customerId) {
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "params",
          message: "customerId is required in URL",
        },
      ],
    });
  }

  const AT = storeData.find((x) => x.shop === shop)?.accessToken;

  if (!AT) {
    throw new AppError("Authentication failed", 401, {
      errors: [
        {
          field: "auth",
          message: "Access token not found for the specified shop.",
        },
      ],
    });
  }

  const result = await getChildProfilesService(shop, AT, customerId);
  return new AppSuccess(res, result);
});

export const updateChildProfiles = catchAsync(async (req, res) => {
  try {
    // Get session
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      throw new AppError("Authentication failed", 401, {
        errors: [
          {
            field: "auth",
            message: "Access token not found for the specified shop.",
          },
        ],
      });
    }

    // Use data from req.body (merged by validation middleware)
    const { customerId, profiles } = req.body;

    const result = await updateChildProfilesService(
      shop,
      AT,
      { customerId, profiles },
      req.files
    );
    return new AppSuccess(res, result);
  } catch (error) {
    if (error instanceof multer.MulterError) {
      if (error.code === "LIMIT_FILE_SIZE") {
        throw new AppError("File too large", 400, {
          errors: [{ field: "image", message: "Image must be under 5MB" }],
        });
      }
      throw new AppError("File upload error", 400, {
        errors: [{ field: "image", message: error.message }],
      });
    }
    throw error;
  }
});
