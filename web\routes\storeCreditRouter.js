import express from "express";
import { getGiftCardBalanceController, redeemGiftCardController } from "../controllers/storeCreditController.js";
import { validateRequest } from "../middlewares/validations.js";
import { cardSchema, redeemCardSchema } from "../utils/validationSchemas/cardSchema.js";

/**
 * @swagger
 * tags:
 *   - name: Store Credit
 *     description: Store credit and gift card management
 */

const router = express.Router();
router.post('/balance',  validateRequest(cardSchema) ,getGiftCardBalanceController);
router.post('/redeem-giftcard',validateRequest(redeemCardSchema), redeemGiftCardController);

export default router;