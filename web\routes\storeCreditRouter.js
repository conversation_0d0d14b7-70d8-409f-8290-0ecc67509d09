import express from "express";
import { qwikcilverAuth, getGiftCardBalanceController, redeemGiftCardController } from "../controllers/storeCreditController.js";

/**
 * @swagger
 * tags:
 *   - name: Store Credit
 *     description: Store credit and gift card management
 */

const router = express.Router();
router.post('/auth', qwikcilverAuth);
router.post('/balance', getGiftCardBalanceController);
router.post('/redeem-giftcard', redeemGiftCardController);

export default router;