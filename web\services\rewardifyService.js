import { getShopifyOrderData } from "../controllers/rewardifyController.js";
import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { rewardifyClient } from "../utils/apiClient.js";
import { catchAsync } from "../utils/helpers.js";
import { createRewardifyHeaders } from "../utils/storeCreditHelpers.js";
import { rewardifyAuth } from "./storeCreditService.js";

export async function getRewardifyOrders(rewardifyToken, orderId) {
  const headers = createRewardifyHeaders(rewardifyToken);
  try {
    const response = await rewardifyClient.get(
      `/order/${orderId}/transactions?page=1&itemsPerPage=100`,
      headers
    );
    return response;
  } catch (error) {
    throw error;
  }
}


export async function creditCustomerInRewardify({
  token,
  shopifyCustomerId,
  customerEmail,
  amount,
  itemTitle,
  orderId,
}) {
  const headers = createRewardifyHeaders(token);
  const body = {
    email: customerEmail,
    amount: amount.toString(),
    memo: `Refund for item "${itemTitle}" from order #${orderId}`,
    expiresAt: "2026-06-19T00:00:00.000Z",
    sendEmail: true,
    emailNote: "Store credit refunded for your return.",
  };

  try {
    return await rewardifyClient.put(
      `/customer/${shopifyCustomerId}/account/credit`,
      body,
      headers
    );
  } catch (error) {
    throw error;
  }
}

// ###################### ORDER REFUND HANDLING ####################
export const processProportionalStoreCredit = catchAsync(async (webhookData, res) => {
  const orderId = webhookData.order_id;
  const refundedLineItems = webhookData.refund_line_items || [];
  const customerEmail = webhookData.email;
  const refundTransactions = webhookData.transactions || [];

  if (!orderId || !refundedLineItems.length) {
    throw new AppError("Missing required webhook data: orderId or refundedLineItems", 400);
  }

  const gateway = refundTransactions.length > 0 ? refundTransactions[0].gateway : null;
  const isCOD = gateway && gateway.toLowerCase().includes('cash on delivery');

  const authResponse = await rewardifyAuth();
  const accessToken = authResponse.access_token;
  if (!accessToken) {
    throw new AppError("No access token received from auth API", 500);
  }

  const transactions = await getRewardifyOrders(accessToken, orderId);
  const foundDiscountRedemption = transactions.some(
    (tx) => tx.transactionType === "Discount Redemption"
  );

  const transactionsData = {
    data: {
      orderId,
      foundDiscountRedemption,
      totalTransactions: transactions.length,
      transactions,
    }
  };

  let storeCreditUsedAmount = 0;
  let transactionCustomerId = null;

  const orderResponse = await getShopifyOrderData(orderId);
  const order = orderResponse.orderData;

  if (order.cancelledAt) {
    console.log("🚫 Order was cancelled, ignoring refund processing - should be handled by cancel webhook");
    return new AppSuccess(res, {
      message: "Order was cancelled - refund processing skipped",
      shouldCredit: false,
      refundAmount: 0
    });
  }

  if (isCOD) {
    transactionCustomerId = order?.customer?.id?.replace("gid://shopify/Customer/", "");

    if (transactionsData.data.foundDiscountRedemption) {
      for (const transaction of transactionsData.data.transactions) {
        if (transaction.transactionType === "Discount Redemption" && transaction.amount) {
          storeCreditUsedAmount += Math.abs(parseFloat(transaction.amount));
        }
      }
    } else {
      storeCreditUsedAmount = 0;
    }
  } else if (transactionsData.data.foundDiscountRedemption) {
    for (const transaction of transactionsData.data.transactions) {
      if (transaction.transactionType === "Discount Redemption" && transaction.amount) {
        storeCreditUsedAmount += Math.abs(parseFloat(transaction.amount));
        if (transaction.shopifyCustomerId && !transactionCustomerId) {
          transactionCustomerId = transaction.shopifyCustomerId;
        }
      }
    }
  } else {
    return new AppSuccess(res, {
      message: "No store credit refund needed - order didn't use store credit",
      refundAmount: 0,
      shouldCredit: false,
      accessToken: accessToken
    });
  }

  if (!transactionCustomerId) {
    throw new AppError("No shopifyCustomerId found in transactions data", 404);
  }

  const finalCustomerId = transactionCustomerId;

  let totalOrderSubtotal = 0;
  const productPrices = {};

  for (const lineItemEdge of order.lineItems.edges) {
    const lineItem = lineItemEdge.node;
    const productPrice = parseFloat(lineItem.variant.price) * lineItem.quantity;
    totalOrderSubtotal += productPrice;
    productPrices[lineItem.variant.id] = {
      unitPrice: parseFloat(lineItem.variant.price),
      quantity: lineItem.quantity,
      totalPrice: productPrice,
      title: lineItem.title
    };
  }

  let totalRefundAmount = 0;
  const refundDetails = [];

  if (isCOD) {
    let totalSubtotal = 0;
    let totalTax = 0;
    let totalProportionalStoreCredit = 0;

    for (const refundedItem of refundedLineItems) {
      const { subtotal, total_tax, line_item } = refundedItem;
      const webhookVariantId = line_item?.variant_id;

      totalSubtotal += (subtotal || 0);
      totalTax += (total_tax || 0);

      let proportionalStoreCredit = 0;
      if (webhookVariantId && storeCreditUsedAmount > 0) {
        const graphqlVariantId = `gid://shopify/ProductVariant/${webhookVariantId}`;
        const productInfo = productPrices[graphqlVariantId];

        if (productInfo) {
          const productProportion = productInfo.totalPrice / totalOrderSubtotal;
          proportionalStoreCredit = storeCreditUsedAmount * productProportion;
          totalProportionalStoreCredit += proportionalStoreCredit;
        }
      }

      refundDetails.push({
        productTitle: line_item?.title || "Unknown Product",
        variantId: webhookVariantId || "unknown",
        subtotal: subtotal || 0,
        tax: total_tax || 0,
        proportionalStoreCredit: proportionalStoreCredit.toFixed(2),
        refundType: storeCreditUsedAmount > 0 ? "COD Refund: Subtotal + Tax + Proportional Store Credit" : "COD Refund: Subtotal + Tax Only"
      });
    }

    const webhookRefundAmount = totalSubtotal + totalTax;
    totalRefundAmount = webhookRefundAmount + totalProportionalStoreCredit;

  } else {
    if (!transactionsData.data?.foundDiscountRedemption || storeCreditUsedAmount === 0) {
      return new AppSuccess(res, {
        message: "No store credit refund needed - order didn't use store credit",
        refundAmount: 0,
        shouldCredit: false
      });
    }

    for (const refundedItem of refundedLineItems) {
      const { quantity, line_item } = refundedItem;
      const webhookVariantId = line_item?.variant_id;
      if (!webhookVariantId) continue;

      const graphqlVariantId = `gid://shopify/ProductVariant/${webhookVariantId}`;
      const productInfo = productPrices[graphqlVariantId];
      if (!productInfo) continue;

      const variantId = graphqlVariantId;
      const productProportion = productInfo.totalPrice / totalOrderSubtotal;
      const productStoreCreditAmount = storeCreditUsedAmount * productProportion;
      const refundProportion = quantity / productInfo.quantity;
      const refundStoreCreditAmount = productStoreCreditAmount * refundProportion;

      totalRefundAmount += refundStoreCreditAmount;

      refundDetails.push({
        productTitle: productInfo.title,
        variantId,
        originalPrice: productInfo.totalPrice,
        refundedQuantity: quantity,
        totalQuantity: productInfo.quantity,
        productProportion: productProportion.toFixed(4),
        productStoreCreditAmount: productStoreCreditAmount.toFixed(2),
        refundProportion: refundProportion.toFixed(4),
        refundStoreCreditAmount: refundStoreCreditAmount.toFixed(2),
        refundType: "Store Credit Only (Non-COD)"
      });
    }
  }

  return new AppSuccess(res, {
    message: "Proportional calculation completed",
    refundAmount: totalRefundAmount.toFixed(2),
    storeCreditUsedAmount: storeCreditUsedAmount.toFixed(2),
    foundDiscountRedemption: transactionsData.foundDiscountRedemption,
    refundDetails,
    shouldCredit: totalRefundAmount > 0,
    gateway,
    isCOD,
    accessToken: accessToken,
    customerData: {
      customerId: finalCustomerId,
      customerEmail,
      orderId,
      orderName: order.name
    }
  });
});




// ###################### ORDER CANCELLED HANDLING ####################

export const orderCancelStoreCredit = async (orderId, customerEmail) => {
  if (!orderId || !customerEmail) {
    throw new AppError("Missing orderId or customerEmail", 400);
  }
  const authResponse = await rewardifyAuth();
  const accessToken = authResponse?.access_token;

  if (!accessToken) {
    throw new AppError("Failed to get access token from Rewardify", 500);
  }
  const transactions = await getRewardifyOrders(accessToken, orderId);
  const storeCreditTransactions = transactions.filter(
    (tx) => tx.transactionType === "Discount Redemption"
  );

  if (storeCreditTransactions.length === 0) {
    return {
      shouldCredit: false,
      refundAmount: 0,
    };
  }
  let totalStoreCredit = 0;
  let customerId = null;

  for (const tx of storeCreditTransactions) {
    totalStoreCredit += Math.abs(parseFloat(tx.amount));
    if (!customerId && tx.shopifyCustomerId) {
      customerId = tx.shopifyCustomerId;
    }
  }

  if (!customerId) {
    throw new AppError("Customer ID not found in store credit transactions", 500);
  }

  return {
    shouldCredit: true,
    refundAmount: totalStoreCredit.toFixed(2),
    accessToken,
    customerData: {
      customerId,
      customerEmail,
      orderId,
    },
  };
};