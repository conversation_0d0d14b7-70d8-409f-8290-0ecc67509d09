
import dotenv from "dotenv";
import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { createRewardifyHeaders } from "../utils/storeCreditHelpers.js";
import { rewardifyAuth } from "./storeCreditService.js";
import { getATFromSQL } from "../middlewares/helpers.js";
import shopify from "../shopify.js";
import { GET_ORDER_QUERY } from "../utils/graphQlQueries.js";
import { createApiError } from "../utils/storeCreditHelpers.js";
import axios from "axios";

dotenv.config();

export async function getRewardifyOrders(rewardifyToken, orderId) {
  const baseUrl = process.env.REWARDIFY_API_BASE_URL;
  console.log("Rewardify get orders URL", baseUrl);
  const headers = createRewardifyHeaders(rewardifyToken);
  try {
    const response = await axios.get(
      `${baseUrl}order/${orderId}/transactions?page=1&itemsPerPage=100`,
      {headers}
    );
    console.log("Rewardify get orders response", response.data);
    return response;

  } catch (error) {
    throw error;
  }
}


export async function creditCustomerInRewardify({
  token,
  shopifyCustomerId,
  customerEmail,
  amount,
  itemTitle,
  orderId,
}) {
  const headers = createRewardifyHeaders(token);
  const body = {
    email: customerEmail,
    amount: amount.toString(),
    memo: `Refund for item "${itemTitle}" from order #${orderId}`,
    expiresAt: "2026-06-19T00:00:00.000Z",
    sendEmail: true,
    emailNote: "Store credit refunded for your return.",
  };

  try {
    return await axios.put(
      `${baseUrl}/customer/${shopifyCustomerId}/account/credit`,
      body,
      headers
    );
  } catch (error) {
    throw error;
  }
}

// Service function to fetch Shopify order data (can be called directly)
export const getShopifyOrderData = async (orderId) => {
  if (!orderId) {
    throw new AppError("Missing orderId parameter", 400, {
      errors: [{
        field: "orderId",
        message: "orderId is required",
        example: "getShopifyOrderData('12345')"
      }]
    });
  }

  // Convert orderId to string if it's a number
  const orderIdString = String(orderId);
  console.log("Processing orderId:", orderIdString, "Type:", typeof orderIdString);

  try {
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      throw new AppError("Authentication failed", 401, {
        errors: [{
          field: "accessToken",
          message: "Access token not found for the specified shop",
          debug: {
            shopFromEnv: shop,
            availableShops: storeData?.map(x => x.shop) || [],
            storeDataCount: storeData?.length || 0
          }
        }]
      });
    }
    const client = new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT }
    });
    const shopifyOrderId = orderIdString.startsWith('gid://')
      ? orderIdString
      : `gid://shopify/Order/${orderIdString}`;
    const { data } = await client.request(GET_ORDER_QUERY, {
      variables: { orderId: shopifyOrderId }
    });

    console.log("Order details" , data)

    if (!data.order) {
      throw new AppError("Order not found", 404, {
        errors: [{
          field: "orderId",
          message: "Order may not exist or you may not have permission to access it",
          orderId: orderIdString,
          shopifyOrderId
        }]
      });
    }

    return {
      message: "Shopify order fetched successfully",
      orderId: orderIdString,
      shopifyOrderId,
      orderData: data.order,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    if (error instanceof AppError) throw error;

    console.error("❌ Error fetching Shopify order:", error);
    console.error("❌ Error details:", {
      message: error.message,
      response: error?.response?.data,
      body: error?.body,
      graphQLErrors: error?.body?.errors?.graphQLErrors
    });

    // Handle different types of errors with enhanced error details
    if (error.message?.includes("401 Unauthorized")) {
      throw new AppError("Unauthorized access to Shopify API", 401, {
        errors: [{
          field: "authentication",
          message: "Unauthorized access to Shopify API",
          suggestions: [
            "Check if the Shopify app is properly installed",
            "Verify that the access token is valid",
            "Ensure the app has 'read_orders' scope",
            "Try reinstalling the Shopify app"
          ]
        }]
      });
    } else if (error.message?.includes("404")) {
      throw new AppError("Order not found", 404, {
        errors: [{
          field: "orderId",
          message: "Order not found",
          suggestions: [
            "Verify the order ID is correct",
            "Check if the order exists in your Shopify store",
            "Ensure you have permission to access this order"
          ]
        }]
      });
    } else if (error?.body?.errors?.graphQLErrors) {
      const graphQLError = error.body.errors.graphQLErrors[0]?.message || "GraphQL error";
      throw new AppError("GraphQL error", 400, {
        errors: [{
          field: "graphql",
          message: graphQLError,
          suggestions: ["Check the GraphQL query syntax and permissions"]
        }]
      });
    }

    throw createApiError("Failed to fetch Shopify order", error, "shopify");
  }
};

// ###################### ORDER REFUND HANDLING ####################
export const processProportionalStoreCredit = async (webhookData) => {
  const orderId = webhookData.order_id;
  const refundedLineItems = webhookData.refund_line_items || [];
  const customerEmail = webhookData.email;
  const refundTransactions = webhookData.transactions || [];

  if (!orderId || !refundedLineItems.length) {
    throw new AppError("Missing required webhook data: orderId or refundedLineItems", 400);
  }

  const gateway = refundTransactions.length > 0 ? refundTransactions[0].gateway : null;
  const isCOD = gateway && gateway.toLowerCase().includes('cash on delivery');

  console.log("Processing proportional store credit refund for order", orderId, "with", refundedLineItems.length, "refunded line items", "and gateway", gateway, "isCOD", isCOD);

  const authResponse = await rewardifyAuth();
  console.log("Auth response received", authResponse.data);
  const accessToken = authResponse.data.access_token;
  if (!accessToken) {
    throw new AppError("No access token received from auth API", 500);
  }

  const transactionsResponse  = await getRewardifyOrders(accessToken, orderId);
const transactionList = transactionsResponse .data;
console.log("Transactions fetched for order with data", transactionList);
  const foundDiscountRedemption = transactionList.some(
    (tx) => tx.transactionType === "Discount Redemption"
  );


  const transactionsData = {
    data: {
      orderId,
      foundDiscountRedemption,
      totalTransactions: transactionList.length,
      transactionList,
    }
  };

  console.log("Transactions data", transactionsData);

  let storeCreditUsedAmount = 0;
  let transactionCustomerId = null;

  const orderResponse = await getShopifyOrderData(orderId);
  const order = orderResponse.orderData;

  if (order.cancelledAt) {
    console.log("🚫 Order was cancelled, ignoring refund processing - should be handled by cancel webhook");
    return {
      success: false,
      message: "Order was cancelled - refund processing skipped",
      shouldCredit: false,
      refundAmount: 0
    };
  }

  if (isCOD) {
    transactionCustomerId = order?.customer?.id?.replace("gid://shopify/Customer/", "");


    if (transactionsData.data.foundDiscountRedemption) {
      for (const transaction of transactionsData.data.transactionList.d) {
        if (transaction.data.transactionType === "Discount Redemption" && transaction.data.amount) {
          storeCreditUsedAmount += Math.abs(parseFloat(transaction.amount));
        }
      }
    } else {
      storeCreditUsedAmount = 0;
    }
  } else if (transactionsData.data.foundDiscountRedemption) {
    for (const transaction of transactionsData.data.transactions) {
      if (transaction.data.transactionType === "Discount Redemption" && transaction.data.amount) {
        storeCreditUsedAmount += Math.abs(parseFloat(transaction.amount));
        if (transaction.shopifyCustomerId && !transactionCustomerId) {
          transactionCustomerId = transaction.shopifyCustomerId;
        }
      }
    }
  } else {
    return {
      success: true,
      message: "No store credit refund needed - order didn't use store credit",
      refundAmount: 0,
      shouldCredit: false,
      accessToken: accessToken
    };
  }

  if (!transactionCustomerId) {
    throw new AppError("No shopifyCustomerId found in transactions data", 404);
  }

  const finalCustomerId = transactionCustomerId;

  let totalOrderSubtotal = 0;
  const productPrices = {};

  for (const lineItemEdge of order.lineItems.edges) {
    const lineItem = lineItemEdge.node;
    const productPrice = parseFloat(lineItem.variant.price) * lineItem.quantity;
    totalOrderSubtotal += productPrice;
    productPrices[lineItem.variant.id] = {
      unitPrice: parseFloat(lineItem.variant.price),
      quantity: lineItem.quantity,
      totalPrice: productPrice,
      title: lineItem.title
    };
  }

  let totalRefundAmount = 0;
  const refundDetails = [];

  if (isCOD) {
    let totalSubtotal = 0;
    let totalTax = 0;
    let totalProportionalStoreCredit = 0;

    for (const refundedItem of refundedLineItems) {
      const { subtotal, total_tax, line_item } = refundedItem;
      const webhookVariantId = line_item?.variant_id;

      totalSubtotal += (subtotal || 0);
      totalTax += (total_tax || 0);

      let proportionalStoreCredit = 0;
      if (webhookVariantId && storeCreditUsedAmount > 0) {
        const graphqlVariantId = `gid://shopify/ProductVariant/${webhookVariantId}`;
        const productInfo = productPrices[graphqlVariantId];

        if (productInfo) {
          const productProportion = productInfo.totalPrice / totalOrderSubtotal;
          proportionalStoreCredit = storeCreditUsedAmount * productProportion;
          totalProportionalStoreCredit += proportionalStoreCredit;
        }
      }

      refundDetails.push({
        productTitle: line_item?.title || "Unknown Product",
        variantId: webhookVariantId || "unknown",
        subtotal: subtotal || 0,
        tax: total_tax || 0,
        proportionalStoreCredit: proportionalStoreCredit.toFixed(2),
        refundType: storeCreditUsedAmount > 0 ? "COD Refund: Subtotal + Tax + Proportional Store Credit" : "COD Refund: Subtotal + Tax Only"
      });
    }

    const webhookRefundAmount = totalSubtotal + totalTax;
    totalRefundAmount = webhookRefundAmount + totalProportionalStoreCredit;

  } else {
    if (!transactionsData.data?.foundDiscountRedemption || storeCreditUsedAmount === 0) {
      return {
        success: true,
        message: "No store credit refund needed - order didn't use store credit",
        refundAmount: 0,
        shouldCredit: false
      };
    }

    for (const refundedItem of refundedLineItems) {
      const { quantity, line_item } = refundedItem;
      const webhookVariantId = line_item?.variant_id;
      if (!webhookVariantId) continue;

      const graphqlVariantId = `gid://shopify/ProductVariant/${webhookVariantId}`;
      const productInfo = productPrices[graphqlVariantId];
      if (!productInfo) continue;

      const variantId = graphqlVariantId;
      const productProportion = productInfo.totalPrice / totalOrderSubtotal;
      const productStoreCreditAmount = storeCreditUsedAmount * productProportion;
      const refundProportion = quantity / productInfo.quantity;
      const refundStoreCreditAmount = productStoreCreditAmount * refundProportion;

      totalRefundAmount += refundStoreCreditAmount;

      refundDetails.push({
        productTitle: productInfo.title,
        variantId,
        originalPrice: productInfo.totalPrice,
        refundedQuantity: quantity,
        totalQuantity: productInfo.quantity,
        productProportion: productProportion.toFixed(4),
        productStoreCreditAmount: productStoreCreditAmount.toFixed(2),
        refundProportion: refundProportion.toFixed(4),
        refundStoreCreditAmount: refundStoreCreditAmount.toFixed(2),
        refundType: "Store Credit Only (Non-COD)"
      });
    }
  }

  return {
    success: true,
    message: "Proportional calculation completed",
    refundAmount: totalRefundAmount.toFixed(2),
    storeCreditUsedAmount: storeCreditUsedAmount.toFixed(2),
    foundDiscountRedemption: transactionsData.foundDiscountRedemption,
    refundDetails,
    shouldCredit: totalRefundAmount > 0,
    gateway,
    isCOD,
    accessToken: accessToken,
    customerData: {
      customerId: finalCustomerId,
      customerEmail,
      orderId,
      orderName: order.name
    }
  };
};





// ###################### ORDER CANCELLED HANDLING ####################

export const orderCancelStoreCredit = async (orderId, customerEmail) => {
  if (!orderId || !customerEmail) {
    throw new AppError("Missing orderId or customerEmail", 400);
  }
  const authResponse = await rewardifyAuth();
  const accessToken = authResponse?.access_token;

  if (!accessToken) {
    throw new AppError("Failed to get access token from Rewardify", 500);
  }
  const transactions = await getRewardifyOrders(accessToken, orderId);
  const storeCreditTransactions = transactions.filter(
    (tx) => tx.transactionType === "Discount Redemption"
  );

  if (storeCreditTransactions.length === 0) {
    return {
      shouldCredit: false,
      refundAmount: 0,
    };
  }
  let totalStoreCredit = 0;
  let customerId = null;

  for (const tx of storeCreditTransactions) {
    totalStoreCredit += Math.abs(parseFloat(tx.amount));
    if (!customerId && tx.shopifyCustomerId) {
      customerId = tx.shopifyCustomerId;
    }
  }

  if (!customerId) {
    throw new AppError("Customer ID not found in store credit transactions", 500);
  }

  return {
    shouldCredit: true,
    refundAmount: totalStoreCredit.toFixed(2),
    accessToken,
    customerData: {
      customerId,
      customerEmail,
      orderId,
    },
  };
};

// ###################### CREDIT STORE CREDIT TO CUSTOMER ####################

// Service function to credit store credit to customer (can be called directly)
export const creditStoreCreditToCustomerService = async (shopifyCustomerId, token, requestData) => {
  if (requestData.webhookData) {
    try {
      // Call proportionate amount calculation function
      const calculationResult = await processProportionalStoreCredit(requestData.webhookData);

      if (!calculationResult.shouldCredit) {
        return {
          message: calculationResult.message,
          refundAmount: calculationResult.refundAmount,
          processed: false
        };
      }

      // Credit the proportional amount to customer account
      const { customerData, refundAmount } = calculationResult;

      const response = await creditCustomerInRewardify({
        token,
        shopifyCustomerId: customerData.customerId,
        customerEmail: customerData.customerEmail,
        amount: refundAmount,
        itemTitle: `${calculationResult.isCOD ? 'COD' : 'Store Credit'} Refund - Order #${customerData.orderName}`,
        orderId: customerData.orderId,
      });

      const transaction = response.data?.transaction || {
        transactionType: "API Manual",
        amount: parseFloat(refundAmount).toFixed(4),
        amountCurrency: "INR"
      };

      return {
        message: "Proportional store credit refund processed successfully",
        refundAmount,
        refundDetails: calculationResult.refundDetails,
        transaction,
        processed: true
      };

    } catch (error) {
      throw createApiError("Failed to process proportional refund", error, "rewardify");
    }
  }

  // Regular manual credit request (existing functionality)
  const { email, amount, memo } = requestData;

  try {
    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we'll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    return { transaction };
  } catch (error) {
    throw createApiError("Failed to credit customer", error, "rewardify");
  }
};