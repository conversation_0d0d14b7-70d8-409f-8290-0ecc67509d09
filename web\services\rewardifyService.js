import { rewardifyClient } from "../utils/apiClient.js";
import { createRewardifyHeaders } from "../utils/storeCreditHelpers.js";

export async function getRewardifyOrders(rewardifyToken, orderId) {
  const headers = createRewardifyHeaders(rewardifyToken);
  try {
    const response = await rewardifyClient.get(
      `/order/${orderId}/transactions?page=1&itemsPerPage=100`,
      headers
    );
    return response;
  } catch (error) {
    throw error;
  }
}


export async function creditCustomerInRewardify({
  token,
  shopifyCustomerId,
  customerEmail,
  amount,
  itemTitle,
  orderId,
}) {
  const headers = createRewardifyHeaders(token);
  const body = {
    email: customerEmail,
    amount: amount.toString(),
    memo: `Refund for item "${itemTitle}" from order #${orderId}`,
    expiresAt: "2026-06-19T00:00:00.000Z",
    sendEmail: true,
    emailNote: "Store credit refunded for your return.",
  };

  try {
    return await rewardifyClient.put(
      `/customer/${shopifyCustomerId}/account/credit`,
      body,
      headers
    );
  } catch (error) {
    throw error;
  }
}