/**
 * @swagger
 * components:
 *   schemas:
 *     ApiSuccess:
 *       type: object
 *       properties:
 *         responseCode:
 *           type: integer
 *           example: 0
 *         status:
 *           type: string
 *           example: "success"
 *         message:
 *           type: string
 *           example: "Operation completed successfully"
 *         data:
 *           type: object
 *           description: Response data varies by endpoint
 *     
 *     ApiError:
 *       type: object
 *       properties:
 *         responseCode:
 *           type: integer
 *           example: 1
 *         status:
 *           type: string
 *           example: "error"
 *         statusCode:
 *           type: integer
 *           example: 400
 *         errors:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 example: "email"
 *               message:
 *                 type: string
 *                 example: "Invalid email format"
 *         stack:
 *           type: string
 *           description: Error stack trace (only in development)
 *     
 *     QwikCilverAuthRequest:
 *       type: object
 *       required:
 *         - transactionId
 *       properties:
 *         transactionId:
 *           type: string
 *           description: 9-digit numeric transaction ID
 *           example: "*********"
 *     
 *     QwikCilverAuthResponse:
 *       type: object
 *       properties:
 *         authToken:
 *           type: string
 *           description: Bearer token for QwikCilver API calls
 *           example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *     
 *     GiftCardBalanceRequest:
 *       type: object
 *       required:
 *         - Cards
 *       properties:
 *         Cards:
 *           type: array
 *           items:
 *             type: object
 *             required:
 *               - CardNumber
 *               - CardPIN
 *             properties:
 *               CardNumber:
 *                 type: string
 *                 description: Gift card number
 *                 example: "1006507003998768"
 *               CardPIN:
 *                 type: string
 *                 description: Gift card PIN
 *                 example: "143048"
 *     
 *     GiftCardBalanceResponse:
 *       type: object
 *       properties:
 *         balance:
 *           type: number
 *           description: Available balance on the gift card
 *           example: 1000
 *         cardStatus:
 *           type: string
 *           description: Status of the gift card
 *           example: "Activated"
 *     
 *     RedeemGiftCardRequest:
 *       type: object
 *       required:
 *         - Cards
 *       properties:
 *         Cards:
 *           type: array
 *           items:
 *             type: object
 *             required:
 *               - CardNumber
 *               - CardPin
 *               - Amount
 *               - InvoiceAmount
 *             properties:
 *               CardNumber:
 *                 type: string
 *                 description: Gift card number
 *                 example: "1006507003998768"
 *               CardPin:
 *                 type: string
 *                 description: Gift card PIN
 *                 example: "143048"
 *               Amount:
 *                 type: string
 *                 description: Amount to redeem from gift card
 *                 example: "100"
 *               InvoiceAmount:
 *                 type: string
 *                 description: Total invoice amount
 *                 example: "500"
 *     
 *     RedeemGiftCardResponse:
 *       type: object
 *       properties:
 *         TransactionAmount:
 *           type: string
 *           description: Amount that was redeemed
 *           example: "100"
 *         Cards:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               Balance:
 *                 type: number
 *                 description: Remaining balance after redemption
 *                 example: 900
 *     
 *     RewardifyAuthResponse:
 *       type: object
 *       properties:
 *         access_token:
 *           type: string
 *           description: Rewardify access token
 *           example: "YTljYzRjMTQ3ZWUyMGQ5OTU4MWNmMWViZDAwNTQxOWM1YzBkMGQ0OTAxZWFmZWViNTIxMDRkOWExMmYzYzZjYw"
 *         token_type:
 *           type: string
 *           example: "Bearer"
 *         expires_in:
 *           type: integer
 *           description: Token expiration time in seconds
 *           example: 3600
 *     
 *     RewardifyCreditRequest:
 *       type: object
 *       required:
 *         - email
 *         - amount
 *         - memo
 *         - sendEmail
 *         - emailNote
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: Customer email address
 *           example: "<EMAIL>"
 *         amount:
 *           type: number
 *           description: Amount to credit to customer account
 *           example: 200
 *         memo:
 *           type: string
 *           description: Memo for the credit transaction
 *           example: "Gift card redemption credit"
 *         expiresAt:
 *           type: string
 *           format: date-time
 *           description: Optional expiration date for the credit
 *           example: "2026-05-05T10:21:05.349Z"
 *         sendEmail:
 *           type: boolean
 *           description: Whether to send email notification to customer
 *           example: true
 *         emailNote:
 *           type: string
 *           description: Note to include in email notification
 *           example: "Your store credit has been added to your account"
 *     
 *     NewsletterRequest:
 *       type: object
 *       required:
 *         - email
 *         - action
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: Customer email address
 *           example: "<EMAIL>"
 *         action:
 *           type: string
 *           enum: [subscribe, unsubscribe]
 *           description: Action to perform (subscribe or unsubscribe)
 *           example: "subscribe"
 *     
 *     NewsletterResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *           description: Success message
 *           example: "Successfully subscribed to newsletter"
 *         data:
 *           type: object
 *           description: Customer data from Shopify
 *           properties:
 *             id:
 *               type: string
 *               example: "gid://shopify/Customer/*********"
 *             email:
 *               type: string
 *               example: "<EMAIL>"
 *             emailMarketingConsent:
 *               type: object
 *               properties:
 *                 marketingState:
 *                   type: string
 *                   example: "SUBSCRIBED"
 *                 marketingOptInLevel:
 *                   type: string
 *                   example: "SINGLE_OPT_IN"
 *   
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *       description: Bearer token for API authentication
 *   
 *   parameters:
 *     AuthorizationHeader:
 *       in: header
 *       name: Authorization
 *       required: true
 *       schema:
 *         type: string
 *         example: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *       description: Bearer token for authentication
 *     
 *     TransactionIdHeader:
 *       in: header
 *       name: TransactionId
 *       required: true
 *       schema:
 *         type: string
 *         example: "*********"
 *       description: 9-digit numeric transaction ID
 *     
 *     DateAtClientHeader:
 *       in: header
 *       name: DateAtClient
 *       required: true
 *       schema:
 *         type: string
 *         format: date-time
 *         example: "2025-06-24T10:57:37.000Z"
 *       description: Current date and time in ISO format
 *     
 *     CustomerIdParam:
 *       in: path
 *       name: customerId
 *       required: true
 *       schema:
 *         type: string
 *         example: "8384376537401"
 *       description: Rewardify customer ID
 */
