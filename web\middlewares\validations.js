import { catchAsync } from "../utils/helpers.js";
import AppError from "./AppError.js";

export const validateRequest = (schema) =>
  catchAsync(async (req, res, next) => {
    let dataToValidate = req.body;

    // Handle multipart form data with JSON string
    if (req.files && req.body.data) {
      try {
        const parsedData = JSON.parse(req.body.data);
        dataToValidate = {
          ...req.files,
          data: parsedData,
        };
      } catch (parseError) {
        return next(
          Object.assign(new AppError("Invalid JSON in data field", 400), {
            name: "ValidationError",
            details: [{ message: "data field must be valid JSON" }],
          })
        );
      }
    }

    const { error, value } = schema.validate(dataToValidate, {
      abortEarly: false,
      allowUnknown: true,
    });

    if (error) {
    //  Handle custom validation errors
     if (error.details[0].type === "any.invalid") {
        throw new AppError("Validation failed", 400, {
          errors: [
            {
              field: error?.details?.[0]?.context?.field || "Server",
              message: error?.details?.[0]?.context?.message || "Something went wrong",
            },
          ],
        });
      }
      return next(
        Object.assign(new AppError("Validation Error", 400), {
          name: "ValidationError",
          details: error.details,
        })
      );
    }

    // Merge validated data back into req.body, keeping files separate
    if (req.files && req.body.data) {
      // For multipart requests, merge the parsed data into req.body
      req.body = { ...req.body, ...value.data };
    } else {
      // For regular JSON requests, merge the entire validated value
      req.body = { ...req.body, ...value };
    }

    next();
  });
