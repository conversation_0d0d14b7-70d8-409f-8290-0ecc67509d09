import express from "express";
import {
  rewardifyAuthController,
  creditRewardifyStoreCreditController,
  creditStoreCreditToCustomer,
  fetchRewardifyTransactions,
  fetchRewardifyOrder
} from "../controllers/rewardifyController.js";
const router = express.Router();

// Auth routes
router.post("/auth", rewardifyAuthController);

// Order routes
router.get("/order/:orderId", fetchRewardifyOrder);
router.get("/order/:orderId/transactions", fetchRewardifyTransactions);

// Customer credit routes
router.put("/customer/:shopifyCustomerId", creditStoreCreditToCustomer);
router.put("/credit/:customerId", creditRewardifyStoreCreditController);

export default router;
