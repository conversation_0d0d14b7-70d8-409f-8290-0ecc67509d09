/**
 * @swagger
 * tags:
 *   - name: Store Credit
 *     description: Store credit and gift card management APIs
 *   - name: QwikCilver
 *     description: QwikCilver gift card integration
 *   - name: Rewardify
 *     description: Rewardify store credit integration
 */

/**
 * @swagger
 * /api/store-credit/auth:
 *   post:
 *     tags: [QwikCilver]
 *     summary: Authenticate with QwikCilver API
 *     description: |
 *       Authenticates with QwikCilver API to obtain an authorization token.
 *       This token is required for all subsequent QwikCilver operations.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/QwikCilverAuthRequest'
 *           example:
 *             transactionId: "123456789"
 *     responses:
 *       200:
 *         description: Authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/QwikCilverAuthResponse'
 *             example:
 *               responseCode: 0
 *               status: "success"
 *               message: "Authentication successful"
 *               data:
 *                 authToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *       400:
 *         description: Bad request - Missing or invalid transaction ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             example:
 *               responseCode: 1
 *               status: "error"
 *               statusCode: 400
 *               errors:
 *                 - field: "body"
 *                   message: "transactionId is required in request body"
 *       401:
 *         description: Authentication failed - Shop access token not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */

/**
 * @swagger
 * /api/store-credit/balance:
 *   post:
 *     tags: [QwikCilver]
 *     summary: Check gift card balance
 *     description: |
 *       Retrieves the balance and status of a gift card using QwikCilver API.
 *       Requires authentication token from /auth endpoint.
 *     parameters:
 *       - $ref: '#/components/parameters/AuthorizationHeader'
 *       - $ref: '#/components/parameters/TransactionIdHeader'
 *       - $ref: '#/components/parameters/DateAtClientHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/GiftCardBalanceRequest'
 *           example:
 *             Cards:
 *               - CardNumber: "1006507003998768"
 *                 CardPIN: "143048"
 *     responses:
 *       200:
 *         description: Balance retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/GiftCardBalanceResponse'
 *             example:
 *               responseCode: 0
 *               status: "success"
 *               message: "Balance retrieved successfully"
 *               data:
 *                 balance: 1000
 *                 cardStatus: "Activated"
 *       400:
 *         description: Bad request - Missing required headers or card data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *       401:
 *         description: Unauthorized - Invalid or missing auth token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *       500:
 *         description: Internal server error or QwikCilver API error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */

/**
 * @swagger
 * /api/store-credit/redeem-giftcard:
 *   post:
 *     tags: [QwikCilver]
 *     summary: Redeem gift card amount
 *     description: |
 *       Redeems a specified amount from a gift card using QwikCilver API.
 *       Validates card balance before redemption and generates unique transaction IDs.
 *     parameters:
 *       - $ref: '#/components/parameters/AuthorizationHeader'
 *       - $ref: '#/components/parameters/TransactionIdHeader'
 *       - $ref: '#/components/parameters/DateAtClientHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RedeemGiftCardRequest'
 *           example:
 *             Cards:
 *               - CardNumber: "1006507003998768"
 *                 CardPin: "143048"
 *                 Amount: "100"
 *                 InvoiceAmount: "500"
 *     responses:
 *       200:
 *         description: Gift card redeemed successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/RedeemGiftCardResponse'
 *             example:
 *               responseCode: 0
 *               status: "success"
 *               message: "Gift card redeemed successfully"
 *               data:
 *                 TransactionAmount: "100"
 *                 Cards:
 *                   - Balance: 900
 *       400:
 *         description: Bad request - Missing required data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *       404:
 *         description: Card not found or invalid
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             example:
 *               responseCode: 1
 *               status: "error"
 *               statusCode: 404
 *               errors:
 *                 - field: "card"
 *                   message: "Card not found or invalid"
 *       500:
 *         description: Internal server error or QwikCilver API error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */

/**
 * @swagger
 * /api/store-credit/rewardify-auth:
 *   post:
 *     tags: [Rewardify]
 *     summary: Authenticate with Rewardify API
 *     description: |
 *       Authenticates with Rewardify API using client credentials to obtain an access token.
 *       This token is required for Rewardify store credit operations.
 *     responses:
 *       200:
 *         description: Authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/RewardifyAuthResponse'
 *             example:
 *               responseCode: 0
 *               status: "success"
 *               message: "Authentication successful"
 *               data:
 *                 access_token: "YTljYzRjMTQ3ZWUyMGQ5OTU4MWNmMWViZDAwNTQxOWM1YzBkMGQ0OTAxZWFmZWViNTIxMDRkOWExMmYzYzZjYw"
 *                 token_type: "Bearer"
 *                 expires_in: 3600
 *       500:
 *         description: Internal server error or Rewardify API error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */

/**
 * @swagger
 * /api/store-credit/rewardify-credit/{customerId}:
 *   put:
 *     tags: [Rewardify]
 *     summary: Credit store credit to customer account
 *     description: |
 *       Credits a specified amount to a customer's Rewardify store credit account.
 *       Validates customer existence before crediting and supports optional expiration dates.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/CustomerIdParam'
 *       - $ref: '#/components/parameters/AuthorizationHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RewardifyCreditRequest'
 *           example:
 *             email: "<EMAIL>"
 *             amount: 200
 *             memo: "Gift card redemption credit"
 *             expiresAt: "2026-05-05T10:21:05.349Z"
 *             sendEmail: true
 *             emailNote: "Your store credit has been added to your account"
 *     responses:
 *       200:
 *         description: Store credit added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiSuccess'
 *             example:
 *               responseCode: 0
 *               status: "success"
 *               message: "Store credit added successfully"
 *               data:
 *                 creditId: "12345"
 *                 amount: 200
 *                 balance: 1200
 *       400:
 *         description: Bad request - Missing required fields or invalid data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             example:
 *               responseCode: 1
 *               status: "error"
 *               statusCode: 400
 *               errors:
 *                 - field: "params/body"
 *                   message: "customerId (in URL), and email, amount, memo, sendEmail, emailNote (in body) are required"
 *       401:
 *         description: Unauthorized - Invalid or missing Rewardify token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *       404:
 *         description: Customer not found in Rewardify system
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             example:
 *               responseCode: 1
 *               status: "error"
 *               statusCode: 404
 *               errors:
 *                 - field: "customerId"
 *                   message: "Customer ID 8384376537401 does not exist in Rewardify system"
 *       500:
 *         description: Internal server error or Rewardify API error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */
