/**
 * @swagger
 * tags:
 *   - name: Store Credit
 *     description: Store credit and gift card management APIs
 */



/**
 * @swagger
 * /api/store-credit/balance:
 *   post:
 *     tags: [Store Credit]
 *     summary: Check gift card balance
 *     description: |
 *       Retrieves the balance and status of a gift card using QwikCilver API.
 *       Authentication is handled automatically by the service.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - CardNumber
 *               - CardPIN
 *             properties:
 *               CardNumber:
 *                 type: string
 *                 description: Gift card number
 *                 example: "1006507003998768"
 *               CardPIN:
 *                 type: string
 *                 description: Gift card PIN
 *                 example: "143048"
 *           example:
 *             CardNumber: "1006507003998768"
 *             CardPIN: "143048"
 *     responses:
 *       200:
 *         description: Balance retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     balance:
 *                       type: number
 *                       description: Current card balance
 *                       example: 1000
 *                     cardStatus:
 *                       type: string
 *                       description: Current card status
 *                       example: "Activated"
 *             example:
 *               responseCode: 0
 *               status: "success"
 *               message: "Balance retrieved successfully"
 *               balance: 1000
 *               cardStatus: "Activated"
 *       400:
 *         description: Bad request - Missing required card data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *       500:
 *         description: Internal server error or QwikCilver API error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */

/**
 * @swagger
 * /api/store-credit/redeem-giftcard:
 *   post:
 *     tags: [Store Credit]
 *     summary: Redeem gift card amount
 *     description: |
 *       Redeems a specified amount from a gift card using QwikCilver API.
 *       Validates card balance before redemption and generates unique transaction IDs.
 *       Authentication is handled automatically by the service.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - CardNumber
 *               - CardPIN
 *               - Amount
 *               - InvoiceAmount
 *             properties:
 *               CardNumber:
 *                 type: string
 *                 description: Gift card number
 *                 example: "1006507003998768"
 *               CardPIN:
 *                 type: string
 *                 description: Gift card PIN
 *                 example: "143048"
 *               Amount:
 *                 type: string
 *                 description: Amount to redeem from the card
 *                 example: "100"
 *               InvoiceAmount:
 *                 type: string
 *                 description: Total invoice amount for the transaction
 *                 example: "500"
 *           example:
 *             CardNumber: "1006507003998768"
 *             CardPIN: "143048"
 *             Amount: "100"
 *             InvoiceAmount: "500"
 *     responses:
 *       200:
 *         description: Gift card redeemed successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         TransactionAmount:
 *                           type: string
 *                           description: Amount that was redeemed
 *                           example: "100"
 *                         Cards:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               Balance:
 *                                 type: number
 *                                 description: Remaining card balance
 *                                 example: 900
 *             example:
 *               responseCode: 0
 *               status: "success"
 *               message: "Gift card redeemed successfully"
 *               data:
 *                 TransactionAmount: "100"
 *                 Cards:
 *                   - Balance: 900
 *       400:
 *         description: Bad request - Missing required data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *       404:
 *         description: Card not found or invalid
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             example:
 *               responseCode: 1
 *               status: "error"
 *               statusCode: 404
 *               errors:
 *                 - field: "card"
 *                   message: "Card not found or invalid"
 *       500:
 *         description: Internal server error or QwikCilver API error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 */


