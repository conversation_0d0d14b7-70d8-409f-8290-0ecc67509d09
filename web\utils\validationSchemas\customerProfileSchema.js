import Joi from "joi";

export const customerUpdateSchema = Joi.object({
  // required fields
  customerId: Joi.string().required(),
  firstName: Joi.string().max(50).required(),
  lastName: Joi.string().max(50).required(),
  email: Joi.string().email().required(),

  // optional fields
  phone: Joi.string()
    // tweak the regex if you need country codes or other formats
    .pattern(/^[0-9]{10}$/)
    .messages({
      "string.pattern.base": "Phone number must be 10 digits",
    })
    .optional(),

  note: Joi.string().optional(),

  tags: Joi.string()
    // allows "tag1,tag2,tag3" (with or without spaces after commas)
    .pattern(/^(\s*[A-Za-z0-9-_]+\s*)(,\s*[A-Za-z0-9-_]+\s*)*$/)
    .messages({
      "string.pattern.base":
        "Tags must be a comma-separated list (e.g., tag1,tag2)",
    })
    .optional(),
});
