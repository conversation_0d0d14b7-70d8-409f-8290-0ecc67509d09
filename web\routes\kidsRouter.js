import express from "express";
import {
  getChildProfiles,
  updateChildProfiles,
} from "../controllers/kidsController.js";
import { verifyProxy } from "../middlewares/proxyVerify.js";
import { validateRequest } from "../middlewares/validations.js";
import { handleMultipleFiles } from "../utils/shopifyImageUpload.js";
import { childProfileFormSchema } from "../utils/validationSchemas/childProfileSchema.js";

const KidsRouter = express.Router();

KidsRouter.post(
  "/",
  verifyProxy,
  handleMultipleFiles,
  validateRequest(childProfileFormSchema),
  updateChildProfiles
);
KidsRouter.get("/:customerId", verifyProxy, getChildProfiles);

export default KidsRouter;
