/**
 * @swagger
 * tags:
 *   - name: Newsletter
 *     description: Newsletter subscription management APIs
 *   - name: Email Marketing
 *     description: Customer email marketing consent management
 */

/**
 * @swagger
 * /api/newsletter/subscribe:
 *   post:
 *     tags: [Newsletter, Email Marketing]
 *     summary: Subscribe or unsubscribe from newsletter
 *     description: |
 *       Manages customer newsletter subscription status using Shopify's customer API.
 *       
 *       **Features:**
 *       - Subscribe new customers to newsletter
 *       - Unsubscribe existing customers from newsletter
 *       - Creates new customer record if subscribing and customer doesn't exist
 *       - Updates existing customer's email marketing consent
 *       - Validates email format before processing
 *       - Prevents duplicate subscription/unsubscription attempts
 *       
 *       **Business Logic:**
 *       - For **subscribe** action: Creates customer if not exists, or updates consent if already exists
 *       - For **unsubscribe** action: Only works if customer already exists (returns 404 if not found)
 *       - Uses Shopify's `SINGLE_OPT_IN` marketing opt-in level
 *       - Automatically detects if customer is already in desired state and returns appropriate message
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/NewsletterRequest'
 *           examples:
 *             subscribe:
 *               summary: Subscribe to newsletter
 *               value:
 *                 email: "<EMAIL>"
 *                 action: "subscribe"
 *             unsubscribe:
 *               summary: Unsubscribe from newsletter
 *               value:
 *                 email: "<EMAIL>"
 *                 action: "unsubscribe"
 *     responses:
 *       200:
 *         description: Newsletter subscription updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/NewsletterResponse'
 *             examples:
 *               subscribed:
 *                 summary: Successfully subscribed
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Successfully subscribed to newsletter"
 *                   data:
 *                     message: "Successfully subscribed to newsletter"
 *                     data:
 *                       id: "gid://shopify/Customer/123456789"
 *                       email: "<EMAIL>"
 *                       emailMarketingConsent:
 *                         marketingState: "SUBSCRIBED"
 *                         marketingOptInLevel: "SINGLE_OPT_IN"
 *               unsubscribed:
 *                 summary: Successfully unsubscribed
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Successfully unsubscribed from newsletter"
 *                   data:
 *                     message: "Successfully unsubscribed from newsletter"
 *                     data:
 *                       id: "gid://shopify/Customer/123456789"
 *                       email: "<EMAIL>"
 *                       emailMarketingConsent:
 *                         marketingState: "UNSUBSCRIBED"
 *                         marketingOptInLevel: "SINGLE_OPT_IN"
 *               already_subscribed:
 *                 summary: Already subscribed
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Already subscribed"
 *                   data:
 *                     message: "Already subscribed"
 *               already_unsubscribed:
 *                 summary: Already unsubscribed
 *                 value:
 *                   responseCode: 0
 *                   status: "success"
 *                   message: "Already unsubscribed"
 *                   data:
 *                     message: "Already unsubscribed"
 *       400:
 *         description: Bad request - Invalid email format or action
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             examples:
 *               invalid_email:
 *                 summary: Invalid email format
 *                 value:
 *                   responseCode: 1
 *                   status: "error"
 *                   statusCode: 400
 *                   errors:
 *                     - field: "email"
 *                       message: "Invalid email format"
 *               invalid_action:
 *                 summary: Invalid action
 *                 value:
 *                   responseCode: 1
 *                   status: "error"
 *                   statusCode: 400
 *                   errors:
 *                     - field: "action"
 *                       message: "Invalid action. Must be 'subscribe' or 'unsubscribe'"
 *       404:
 *         description: Customer not found (only for unsubscribe action)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             example:
 *               responseCode: 1
 *               status: "error"
 *               statusCode: 404
 *               errors:
 *                 - field: "customer"
 *                   message: "Customer not found"
 *       500:
 *         description: Internal server error or Shopify API error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             examples:
 *               access_token_error:
 *                 summary: Access token not found
 *                 value:
 *                   responseCode: 1
 *                   status: "error"
 *                   statusCode: 500
 *                   errors:
 *                     - field: "auth"
 *                       message: "Access token not found"
 *               shopify_api_error:
 *                 summary: Shopify API error
 *                 value:
 *                   responseCode: 1
 *                   status: "error"
 *                   statusCode: 500
 *                   errors:
 *                     - field: "shopify"
 *                       message: "Shopify API Error"
 *                     - field: "details"
 *                       message: "Customer email is already taken"
 */
