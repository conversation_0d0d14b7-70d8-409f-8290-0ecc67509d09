import AppError from "../middlewares/AppError.js";
import { UPDATE_CUSTOMER_PROFILE_MUTATION } from "../utils/graphQlQueries.js";
import shopify from "../shopify.js";
import { getATFromSQL } from "../middlewares/helpers.js";

export const updateCustomerProfileService = async (data) => {
  const { customerId, firstName, lastName, email, phone, note, tags } = data;

  // Get shop and access token
  const storeData = await getATFromSQL();
  const shop = process.env.SHOP;
  const AT = storeData.find((x) => x.shop === shop)?.accessToken;

  if (!AT) {
    throw new AppError("Authentication failed", 401, {
      errors: [
        {
          field: "auth",
          message: "Access token not found for the specified shop.",
        },
      ],
    });
  }

  const variables = {
    input: {
      id: `gid://shopify/Customer/${customerId}`,
      firstName,
      lastName,
      email,
      phone,
      note,
      tags,
    },
  };

  const { data: result } = await new shopify.api.clients.Graphql({
    session: { shop, accessToken: AT },
  }).request(UPDATE_CUSTOMER_PROFILE_MUTATION, { variables });

  if (result.customerUpdate.userErrors?.length > 0) {
    throw new AppError("Failed to update customer", 400, {
      errors: result.customerUpdate.userErrors.map((err) => ({
        field: err.field?.[0] || "shopify",
        message: err.message,
      })),
    });
  }

  return result;
};
