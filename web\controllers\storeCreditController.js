import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { authorizeQwikCilver, getGiftCardBalance, redeemGiftCard } from "../services/storeCreditService.js";
import {
  generateInvoiceData,
  generateStringTransactionId,
  createRedeemPayload,
  createRedeemNotes,

} from "../utils/storeCreditHelpers.js";




export const getGiftCardBalanceController = catchAsync(async (req, res) => {
  const { CardNumber, CardPIN } = req.body;

  const transactionId = generateStringTransactionId();
  const authResult = await authorizeQwikCilver(transactionId);
  const authToken = authResult.AuthToken;

  const result = await getGiftCardBalance({
    authToken,
    transactionId,
    cardNumber: CardNumber,
    cardPIN: CardPIN,
  });

  const card = result.Cards[0];

  return new AppSuccess(res, {
    balance: card.Balance,
    cardStatus: card.CardStatus,
  });
});


export const redeemGiftCardController = catchAsync(async (req, res) => {
  const { CardNumber, CardPIN, Amount } = req.body;

  const balanceCheckTransactionId = generateStringTransactionId();
  const redeemTransactionId = generateStringTransactionId();
  const dateAtClient = formattedDate();
  const authResult = await authorizeQwikCilver(balanceCheckTransactionId);
  const authToken = authResult.AuthToken;

  const { invoiceNumber, idempotencyKey } = generateInvoiceData();
  const notes = createRedeemNotes({ amount: Amount, invoiceAmount: Amount, invoiceNumber });

  const balanceResult = await getGiftCardBalance({
    authToken,
    transactionId: balanceCheckTransactionId,
    cardNumber: CardNumber,
    cardPIN: CardPIN,
  });

  if (!balanceResult.Cards || balanceResult.Cards.length === 0) {
    throw new AppError("Card not found or invalid", 404);
  }

  const payload = createRedeemPayload({
    invoiceNumber,
    idempotencyKey,
    cardData: { CardNumber, CardPin: CardPIN, Amount, InvoiceAmount: Amount },
    notes,
  });

  const redeemResult = await redeemGiftCard({
    authToken,
    transactionId: redeemTransactionId,
    dateAtClient,
    payload,
  });

  return new AppSuccess(res, {
    TransactionAmount: redeemResult?.Cards?.[0]?.TransactionAmount || Amount,
    Cards: [{ Balance: redeemResult?.Cards?.[0]?.Balance }],
  });
});







