import { GET_CUSTOMER_BY_EMAIL, CREATE_CUSTOMER, UPDATE_CUSTOMER_EMAIL_MARKETING } from "../utils/newletterQueries.js"
import AppError from "../middlewares/AppError.js";
import AppSuccess from '../middlewares/appSuccess.js';
import { getATFromSQL } from "../middlewares/helpers.js";
import shopify from "../shopify.js";
import { catchAsync } from "../utils/helpers.js";
import validator from "validator";

const findCustomerByEmail = async (shop, accessToken, email) => {
  const client = new shopify.api.clients.Graphql({ session: { shop, accessToken } });
  const { data } = await client.request(GET_CUSTOMER_BY_EMAIL, {
    variables: { email }
  });
  return data.customers.edges.length > 0 ? data.customers.edges[0].node : null;
};
// ####################  SUBSCRIBE EMAIL ####################

export const handleNewsletter = catchAsync(async (req, res) => {
  const { email, action } = req.body;

  if (!validator.isEmail(email)) {
    throw new AppError("Invalid email format", 400, {
      errors: [{ field: "email", message: "Invalid email format" }]
    });
  }

  if (!["subscribe", "unsubscribe"].includes(action)) {
    throw new AppError("Invalid action. Must be 'subscribe' or 'unsubscribe'", 400, {
      errors: [{ field: "action", message: "Invalid action. Must be 'subscribe' or 'unsubscribe'" }]
    });
  }

  const storeData = await getATFromSQL();
  const shop = process.env.SHOP;
  const AT = storeData.find((x) => x.shop === shop)?.accessToken;

  if (!AT) throw new AppError("Access token not found", 500, {
    errors: [{ field: "accessToken", message: "Access token not found" }]
  });

  const client = new shopify.api.clients.Graphql({ session: { shop, accessToken: AT } });

  const customer = await findCustomerByEmail(shop, AT, email);
  const isSubscribe = action === "subscribe";

  const marketingState = isSubscribe ? "SUBSCRIBED" : "UNSUBSCRIBED";

  if (customer) {
    const currentState = customer.emailMarketingConsent?.marketingState;
    if (currentState === marketingState) {
      return new AppSuccess(res, { message: `Already ${isSubscribe ? "subscribed" : "unsubscribed"}` });
    }

    const { data } = await client.request(UPDATE_CUSTOMER_EMAIL_MARKETING, {
      variables: {
        input: {
          customerId: customer.id,
          emailMarketingConsent: {
            marketingState,
            marketingOptInLevel: "SINGLE_OPT_IN"
          }
        }
      }
    });

    const errors = data.customerEmailMarketingConsentUpdate.userErrors;
    if (errors.length > 0) {
      throw new AppError("Shopify API Error", 500, {
        errors: errors.map(e => ({ field: e.field || "shopify", message: e.message }))
      });
    }

    return new AppSuccess(res, {
      message: `Successfully ${isSubscribe ? "subscribed" : "unsubscribed"} from newsletter`,
      data: data.customerEmailMarketingConsentUpdate.customer
    });
  } else {
    if (!isSubscribe) {
      throw new AppError("Customer not found", 404, {
        errors: [{ field: "customer", message: "Customer not found" }]
      });
    }

    const { data } = await client.request(CREATE_CUSTOMER, {
      variables: {
        input: {
          email,
          emailMarketingConsent: {
            marketingState: "SUBSCRIBED",
            marketingOptInLevel: "SINGLE_OPT_IN"
          }
        }
      }
    });

    if (data.customerCreate.userErrors.length > 0) {
      throw new AppError("Shopify API Error", 500, {
        errors: data.customerCreate.userErrors.map(e => ({ field: e.field || "shopify", message: e.message }))
      });
    }

    return new AppSuccess(res, {
      message: "Successfully subscribed to newsletter",
      data: data.customerCreate.customer
    });
  }
});

