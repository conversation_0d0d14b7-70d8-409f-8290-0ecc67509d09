// Consolidated swagger documentation
import swaggerJSD<PERSON> from 'swagger-jsdoc';

// Function to get current server URL dynamically
const getCurrentServerUrl = () => {
  // Try to get from environment first
  if (process.env.SHOPIFY_APP_URL) {
    return process.env.SHOPIFY_APP_URL;
  }

  // Fallback to a default (this will be updated by the server when serving)
  return 'https://hollow-celebration-dsc-excuse.trycloudflare.com';
};

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'MiniKlub Store Credit App API',
    version: '1.0.0',
    description: 'API for managing store credits, gift cards, and newsletter subscriptions',
    contact: {
      name: 'MiniKlub Development Team',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: getCurrentServerUrl(),
      description: 'Development server'
    },
    {
      url: 'http://localhost:3000',
      description: 'Local development server'
    }
  ],
  components: {
    schemas: {
      ApiSuccess: {
        type: 'object',
        properties: {
          responseCode: { type: 'integer', example: 0 },
          status: { type: 'string', example: 'success' },
          message: { type: 'string' }
        }
      },
      ApiError: {
        type: 'object',
        properties: {
          responseCode: { type: 'integer', example: 1 },
          status: { type: 'string', example: 'error' },
          statusCode: { type: 'integer', example: 400 },
          errors: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                field: { type: 'string' },
                message: { type: 'string' }
              }
            }
          }
        }
      },
      NewsletterRequest: {
        type: 'object',
        required: ['email', 'action'],
        properties: {
          email: { type: 'string', format: 'email', example: '<EMAIL>' },
          action: { type: 'string', enum: ['subscribe', 'unsubscribe'], example: 'subscribe' }
        }
      },
      QwikCilverAuthRequest: {
        type: 'object',
        required: ['transactionId'],
        properties: {
          transactionId: { type: 'string', example: '123456789' }
        }
      }
    },
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Bearer token obtained from Rewardify auth API'
      }
    }
  },
  paths: {
    '/api/healthcheck': {
      get: {
        tags: ['System'],
        summary: 'Health check endpoint',
        description: 'Returns the health status of the API',
        responses: {
          '200': {
            description: 'API is healthy',
            content: {
              'text/plain': {
                schema: {
                  type: 'string',
                  example: 'HealthCheck - OK with EB!!!'
                }
              }
            }
          }
        }
      }
    },
    '/api/newsletter/subscribe': {
      post: {
        tags: ['Newsletter'],
        summary: 'Subscribe or unsubscribe from newsletter',
        description: 'Manages customer newsletter subscription status using Shopify customer API',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/NewsletterRequest' },
              examples: {
                subscribe: {
                  summary: 'Subscribe to newsletter',
                  value: { email: '<EMAIL>', action: 'subscribe' }
                },
                unsubscribe: {
                  summary: 'Unsubscribe from newsletter',
                  value: { email: '<EMAIL>', action: 'unsubscribe' }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Subscription updated successfully',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: { type: 'string', example: 'Successfully subscribed to newsletter' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ApiError' }
              }
            }
          }
        }
      }
    },
    '/api/store-credit/balance': {
      post: {
        tags: ['QwikCilver'],
        summary: 'Check gift card balance',
        description: 'Check the balance and status of a gift card. All authentication headers (Bearer token, TransactionId, DateAtClient) are managed automatically by the backend.',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  CardNumber: { type: 'string', description: 'Gift card number', example: '1006507003998768' },
                  CardPIN: { type: 'string', description: 'Gift card PIN', example: '143048' }
                },
                required: ['CardNumber', 'CardPIN']
              },
              example: { CardNumber: '1006507003998768', CardPIN: '143048' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Balance retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            balance: { type: 'number', example: 1000 },
                            cardStatus: { type: 'string', example: 'Activated' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request - Invalid card details or missing headers',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ApiError' }
              }
            }
          },
          '401': {
            description: 'Unauthorized - Invalid or expired token',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ApiError' }
              }
            }
          }
        }
      }
    },
    '/api/store-credit/redeem-giftcard': {
      post: {
        tags: ['QwikCilver'],
        summary: 'Redeem gift card',
        description: 'Redeem a gift card for a specific amount. All authentication headers (Bearer token, TransactionId, DateAtClient) are managed automatically by the backend.',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  CardNumber: { type: 'string', description: 'Gift card number', example: '1006507003998768' },
                  CardPIN: { type: 'string', description: 'Gift card PIN', example: '143048' },
                  Amount: { type: 'string', description: 'Amount to redeem', example: '100.00' }
                },
                required: ['CardNumber', 'CardPIN', 'Amount']
              },
              example: { CardNumber: '1006507003998768', CardPIN: '143048', Amount: '100.00' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Gift card redeemed successfully',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            transactionId: { type: 'string', example: '123456789' },
                            redeemedAmount: { type: 'number', example: 500 },
                            remainingBalance: { type: 'number', example: 500 }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request - Invalid card details, insufficient balance, or missing headers',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ApiError' }
              }
            }
          },
          '401': {
            description: 'Unauthorized - Invalid or expired token',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ApiError' }
              }
            }
          }
        }
      }
    },
    '/api/webhooks/refund': {
      post: {
        tags: ['Webhooks'],
        summary: 'Handle Shopify refund webhooks',
        description: 'Processes Shopify refund webhooks to automatically credit proportional store credit back to customers',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  order_id: { type: 'integer', example: 11749951209841 },
                  email: { type: 'string', example: '<EMAIL>' },
                  refund_line_items: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        quantity: { type: 'integer', example: 1 },
                        subtotal: { type: 'number', example: 999 },
                        total_tax: { type: 'number', example: 179.82 },
                        line_item: {
                          type: 'object',
                          properties: {
                            variant_id: { type: 'integer', example: 55236414144881 },
                            title: { type: 'string', example: 'Miniklub Boys Brown Lace Up Hard Sole Shoes' }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Refund webhook processed successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    responseCode: { type: 'integer', example: 0 },
                    status: { type: 'string', example: 'success' },
                    message: { type: 'string', example: 'Refund webhook processed with store credit refund' },
                    data: {
                      type: 'object',
                      properties: {
                        refundAmount: { type: 'string', example: '100.00' },
                        transaction: { type: 'object' }
                      }
                    }
                  }
                }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ApiError' }
              }
            }
          }
        }
      }
    },
    '/api/webhooks/cancel': {
      post: {
        tags: ['Webhooks'],
        summary: 'Handle Shopify order cancellation webhooks',
        description: 'Processes Shopify order cancellation webhooks to automatically refund store credit',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  id: { type: 'integer', example: 11749951209841 },
                  customer: {
                    type: 'object',
                    properties: {
                      email: { type: 'string', example: '<EMAIL>' }
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Cancellation webhook processed successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    responseCode: { type: 'integer', example: 0 },
                    status: { type: 'string', example: 'success' },
                    message: { type: 'string', example: 'Cancel webhook processed with store credit refund' },
                    data: {
                      type: 'object',
                      properties: {
                        orderId: { type: 'integer', example: 11749951209841 },
                        customerId: { type: 'string', example: '23476814184817' },
                        refundAmount: { type: 'string', example: '200.00' }
                      }
                    }
                  }
                }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ApiError' }
              }
            }
          }
        }
      }
    }
    // ...add other paths as needed...
  }
};

const options = {
  swaggerDefinition,
  apis: [] // No external files, everything is defined above
};

const consolidatedSwaggerSpec = swaggerJSDoc(options);
export default consolidatedSwaggerSpec;
