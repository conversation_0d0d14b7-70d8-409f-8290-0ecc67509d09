// Consolidated swagger documentation
import swaggerJSDoc from 'swagger-jsdoc';

// Function to get current server URL dynamically
const getCurrentServerUrl = () => {
  // Try to get from environment first
  if (process.env.SHOPIFY_APP_URL) {
    return process.env.SHOPIFY_APP_URL;
  }

  // Fallback to a default (this will be updated by the server when serving)
  return 'https://hollow-celebration-dsc-excuse.trycloudflare.com';
};

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'MiniKlub Store Credit App API',
    version: '1.0.0',
    description: 'API for managing store credits, gift cards, and newsletter subscriptions',
    contact: {
      name: 'MiniKlub Development Team',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: getCurrentServerUrl(),
      description: 'Development server'
    },
    {
      url: 'http://localhost:3000',
      description: 'Local development server'
    }
  ],
  components: {
    schemas: {
      ApiSuccess: {
        type: 'object',
        properties: {
          responseCode: {
            type: 'integer',
            example: 0
          },
          status: {
            type: 'string',
            example: 'success'
          },
          message: {
            type: 'string',
            example: 'Operation completed successfully'
          },
          data: {
            type: 'object',
            description: 'Response data varies by endpoint'
          }
        }
      },
      ApiError: {
        type: 'object',
        properties: {
          responseCode: {
            type: 'integer',
            example: 1
          },
          status: {
            type: 'string',
            example: 'error'
          },
          statusCode: {
            type: 'integer',
            example: 400
          },
          errors: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                field: {
                  type: 'string',
                  example: 'email'
                },
                message: {
                  type: 'string',
                  example: 'Invalid email format'
                }
              }
            }
          }
        }
      },
      NewsletterRequest: {
        type: 'object',
        required: ['email', 'action'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>'
          },
          action: {
            type: 'string',
            enum: ['subscribe', 'unsubscribe'],
            example: 'subscribe'
          }
        }
      },
      QwikCilverAuthRequest: {
        type: 'object',
        required: ['transactionId'],
        properties: {
          transactionId: {
            type: 'string',
            example: '*********'
          }
        }
      }
    },
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Bearer token obtained from Rewardify auth API'
      }
    }
  },
  paths: {
    '/api/healthcheck': {
      get: {
        tags: ['System'],
        summary: 'Health check endpoint',
        description: 'Returns the health status of the API',
        responses: {
          '200': {
            description: 'API is healthy',
            content: {
              'text/plain': {
                schema: {
                  type: 'string',
                  example: 'HealthCheck - OK with EB!!!'
                }
              }
            }
          }
        }
      }
    },
    '/api/newsletter/subscribe': {
      post: {
        tags: ['Newsletter'],
        summary: 'Subscribe or unsubscribe from newsletter',
        description: 'Manages customer newsletter subscription status using Shopify customer API',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/NewsletterRequest'
              },
              examples: {
                subscribe: {
                  summary: 'Subscribe to newsletter',
                  value: {
                    email: '<EMAIL>',
                    action: 'subscribe'
                  }
                },
                unsubscribe: {
                  summary: 'Unsubscribe from newsletter',
                  value: {
                    email: '<EMAIL>',
                    action: 'unsubscribe'
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Subscription updated successfully',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: {
                              type: 'string',
                              example: 'Successfully subscribed to newsletter'
                            }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          }
        }
      }
    },
    '/api/store-credit/auth': {
      post: {
        tags: ['QwikCilver'],
        summary: 'Authenticate with QwikCilver API',
        description: 'Authenticates with QwikCilver API to obtain an authorization token. Transaction ID is generated automatically on the backend.',
        requestBody: {
          required: false,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {},
                description: 'No request body required - all parameters are generated automatically'
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Authentication successful',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            authToken: {
                              type: 'string',
                              example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                            },
                            transactionId: {
                              type: 'string',
                              example: '*********',
                              description: 'Auto-generated transaction ID'
                            }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request - Missing or invalid transaction ID',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          }
        }
      }
    },
    '/api/store-credit/balance': {
      post: {
        tags: ['QwikCilver'],
        summary: 'Check gift card balance',
        description: 'Check the balance and status of a gift card. All authentication headers (Bearer token, TransactionId, DateAtClient) are managed automatically by the backend.',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  CardNumber: {
                    type: 'string',
                    description: 'Gift card number',
                    example: '1006507003998768'
                  },
                  CardPIN: {
                    type: 'string',
                    description: 'Gift card PIN',
                    example: '143048'
                  }
                },
                required: ['CardNumber', 'CardPIN']
              },
              example: {
                CardNumber: '1006507003998768',
                CardPIN: '143048'
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Balance retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            balance: {
                              type: 'number',
                              example: 1000
                            },
                            cardStatus: {
                              type: 'string',
                              example: 'Activated'
                            }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request - Invalid card details or missing headers',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          },
          '401': {
            description: 'Unauthorized - Invalid or expired token',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          }
        }
      }
    },
    '/api/store-credit/redeem-giftcard': {
      post: {
        tags: ['QwikCilver'],
        summary: 'Redeem gift card',
        description: 'Redeem a gift card for a specific amount. All authentication headers (Bearer token, TransactionId, DateAtClient) are managed automatically by the backend.',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  CardNumber: {
                    type: 'string',
                    description: 'Gift card number',
                    example: '1006507003998768'
                  },
                  CardPIN: {
                    type: 'string',
                    description: 'Gift card PIN',
                    example: '143048'
                  },
                  Amount: {
                    type: 'string',
                    description: 'Amount to redeem',
                    example: '100.00'
                  }
                },
                required: ['CardNumber', 'CardPIN', 'Amount']
              },
              example: {
                CardNumber: '1006507003998768',
                CardPIN: '143048',
                Amount: '100.00'
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Gift card redeemed successfully',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            transactionId: {
                              type: 'string',
                              example: '*********'
                            },
                            redeemedAmount: {
                              type: 'number',
                              example: 500
                            },
                            remainingBalance: {
                              type: 'number',
                              example: 500
                            }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request - Invalid card details, insufficient balance, or missing headers',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          },
          '401': {
            description: 'Unauthorized - Invalid or expired token',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          }
        }
      }
    },
    '/api/rewardify/auth': {
      post: {
        tags: ['Rewardify'],
        summary: 'Authenticate with Rewardify API',
        description: 'Authenticates with Rewardify API to obtain an authorization token required for store credit operations',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  transactionId: {
                    type: 'string',
                    example: '*********',
                    description: 'Unique transaction ID for this operation'
                  }
                },
                required: ['transactionId']
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Authentication successful',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            authToken: {
                              type: 'string',
                              example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                            }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request - Missing or invalid transaction ID',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          }
        }
      }
    },
    '/api/rewardify/credit/{customerId}': {
      put: {
        tags: ['Rewardify'],
        summary: 'Credit store credit to customer account',
        description: 'Credits store credit amount to a customer account using Rewardify API',
        parameters: [
          {
            in: 'path',
            name: 'customerId',
            required: true,
            description: 'Shopify customer ID',
            schema: {
              type: 'string',
              example: '*************'
            }
          },
          {
            in: 'header',
            name: 'Authorization',
            required: true,
            description: 'Bearer token obtained from /rewardify-auth endpoint',
            schema: {
              type: 'string',
              example: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
            }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  amount: {
                    type: 'number',
                    example: 500,
                    description: 'Amount to credit to customer account'
                  },
                  transactionId: {
                    type: 'string',
                    example: '*********',
                    description: 'Unique transaction ID for this operation'
                  },
                  description: {
                    type: 'string',
                    example: 'Gift card redemption credit',
                    description: 'Description for the credit transaction'
                  }
                },
                required: ['amount', 'transactionId']
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Store credit added successfully',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/ApiSuccess' },
                    {
                      type: 'object',
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            customerId: {
                              type: 'string',
                              example: '*************'
                            },
                            creditedAmount: {
                              type: 'number',
                              example: 500
                            },
                            totalStoreCredit: {
                              type: 'number',
                              example: 1500,
                              description: 'Total store credit balance after this transaction'
                            },
                            transactionId: {
                              type: 'string',
                              example: '*********'
                            }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Bad request - Invalid customer ID, amount, or missing data',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          },
          '401': {
            description: 'Unauthorized - Invalid or expired token',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          },
          '404': {
            description: 'Customer not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError'
                }
              }
            }
          }
        }
      }
    },

    // ==================== REWARDIFY API ROUTES ====================
    '/api/rewardify/order/{orderId}': {
      get: {
        tags: ['Rewardify'],
        summary: 'Fetch Shopify order details',
        description: 'Retrieve comprehensive order details from Shopify including customer info, line items, transactions, and discount applications',
        parameters: [
          {
            name: 'orderId',
            in: 'path',
            required: true,
            description: 'Shopify order ID (numeric or GraphQL format)',
            schema: {
              type: 'string',
              example: '11742448583025'
            }
          }
        ],
        responses: {
          200: {
            description: 'Order details retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    orderId: {
                      type: 'string',
                      example: '11742448583025'
                    },
                    orderName: {
                      type: 'string',
                      example: '#1065'
                    },
                    customer: {
                      type: 'object',
                      properties: {
                        id: { type: 'string', example: 'gid://shopify/Customer/**************' },
                        email: { type: 'string', example: '<EMAIL>' },
                        firstName: { type: 'string', example: 'John' },
                        lastName: { type: 'string', example: 'Doe' }
                      }
                    },
                    lineItems: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          title: { type: 'string', example: 'Product Name' },
                          quantity: { type: 'integer', example: 1 },
                          variant: {
                            type: 'object',
                            properties: {
                              id: { type: 'string', example: 'gid://shopify/ProductVariant/55236244930929' },
                              price: { type: 'string', example: '1499.00' }
                            }
                          }
                        }
                      }
                    },
                    totalPrice: {
                      type: 'string',
                      example: '1796.86'
                    },
                    subtotalPrice: {
                      type: 'string',
                      example: '1499.00'
                    }
                  }
                }
              }
            }
          },
          400: {
            description: 'Bad request - Missing order ID',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Missing orderId in URL params' },
                    example: { type: 'string', example: 'GET /api/rewardify/order/12345' }
                  }
                }
              }
            }
          },
          401: {
            description: 'Authentication failed - Access token not found',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Authentication failed' },
                    error: { type: 'string', example: 'Access token not found for the specified shop' }
                  }
                }
              }
            }
          },
          500: {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Failed to fetch order details' }
                  }
                }
              }
            }
          }
        }
      }
    },

    '/api/rewardify/order/{orderId}/transactions': {
      get: {
        tags: ['Rewardify'],
        summary: 'Fetch Rewardify transactions for order',
        description: 'Retrieve store credit transactions from Rewardify API for a specific order to check if store credit was used',
        parameters: [
          {
            name: 'orderId',
            in: 'path',
            required: true,
            description: 'Shopify order ID',
            schema: {
              type: 'string',
              example: '11742448583025'
            }
          },

        ],
        security: [
          {
            bearerAuth: []
          }
        ],
        responses: {
          200: {
            description: 'Transactions retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    orderId: {
                      type: 'string',
                      example: '11742448583025'
                    },
                    foundDiscountRedemption: {
                      type: 'boolean',
                      description: 'Whether store credit was used in this order',
                      example: true
                    },
                    totalTransactions: {
                      type: 'integer',
                      example: 1
                    },
                    transactions: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'integer', example: 9246103 },
                          shopifyId: { type: 'string', nullable: true },
                          shopifyCustomerId: { type: 'string', example: '**************' },
                          transactionType: { type: 'string', example: 'Discount Redemption' },
                          effectiveAt: { type: 'string', format: 'date-time', example: '2025-06-27T11:14:51+00:00' },
                          customerOpenBalance: { type: 'string', example: '3467.6500' },
                          amount: { type: 'string', example: '-100.0000' },
                          amountCurrency: { type: 'string', example: 'INR' },
                          memo: { type: 'string', nullable: true },
                          expiresAt: { type: 'string', nullable: true },
                          expiredAt: { type: 'string', nullable: true }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: 'Bad request - Missing order ID',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Missing orderId in URL params' }
                  }
                }
              }
            }
          },
          401: {
            description: 'Unauthorized - Missing or invalid authorization token',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Missing Authorization token in headers' }
                  }
                }
              }
            }
          },
          500: {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Failed to fetch Rewardify transactions' }
                  }
                }
              }
            }
          }
        }
      }
    },

    '/api/rewardify/customer/{shopifyCustomerId}': {
      put: {
        tags: ['Rewardify'],
        summary: 'Credit store credit to customer account',
        description: 'Credit store credit amount to customer account in Rewardify. Supports both direct crediting and webhook-based proportional refund processing.',
        parameters: [
          {
            name: 'shopifyCustomerId',
            in: 'path',
            required: true,
            description: 'Shopify customer ID',
            schema: {
              type: 'string',
              example: '**************'
            }
          },

        ],
        security: [
          {
            bearerAuth: []
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                oneOf: [
                  {
                    title: 'Direct Credit Request',
                    type: 'object',
                    required: ['email', 'memo', 'amount'],
                    properties: {
                      email: {
                        type: 'string',
                        format: 'email',
                        description: 'Customer email address',
                        example: '<EMAIL>'
                      },
                      memo: {
                        type: 'string',
                        description: 'Transaction memo/description',
                        example: 'Store credit refund for order #1065'
                      },
                      expiresAt: {
                        type: 'string',
                        format: 'date-time',
                        description: 'Credit expiration date (optional)',
                        example: '2026-06-19T00:00:00.000Z'
                      },
                      sendEmail: {
                        type: 'boolean',
                        description: 'Whether to send email notification',
                        example: true
                      },
                      emailNote: {
                        type: 'string',
                        description: 'Custom note for email notification',
                        example: 'Your store credit refund has been processed.'
                      },
                      amount: {
                        type: 'string',
                        description: 'Amount to credit (as string)',
                        example: '275.50'
                      }
                    }
                  },
                  {
                    title: 'Webhook Refund Request',
                    type: 'object',
                    required: ['webhookData'],
                    properties: {
                      webhookData: {
                        type: 'object',
                        description: 'Shopify refund webhook data for proportional store credit processing',
                        properties: {
                          id: { type: 'integer', example: ************* },
                          order_id: { type: 'integer', example: ************** },
                          refund_line_items: {
                            type: 'array',
                            items: {
                              type: 'object',
                              properties: {
                                id: { type: 'integer' },
                                quantity: { type: 'integer', example: 1 },
                                subtotal: { type: 'number', example: 197.15 },
                                total_tax: { type: 'number', example: 35.49 },
                                line_item: {
                                  type: 'object',
                                  properties: {
                                    id: { type: 'integer' },
                                    variant_id: { type: 'integer', example: ************** },
                                    title: { type: 'string', example: 'Product Name' },
                                    price: { type: 'string', example: '300.00' }
                                  }
                                }
                              }
                            }
                          },
                          transactions: {
                            type: 'array',
                            items: {
                              type: 'object',
                              properties: {
                                gateway: { type: 'string', example: 'Cash on Delivery (COD)' },
                                amount: { type: 'string', example: '232.64' }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                ]
              },
              examples: {
                directCredit: {
                  summary: 'Direct credit to customer account',
                  value: {
                    email: '<EMAIL>',
                    memo: 'Store credit refund for order #1065',
                    amount: '275.50',
                    sendEmail: true,
                    emailNote: 'Your store credit refund has been processed.'
                  }
                },
                webhookRefund: {
                  summary: 'Webhook-based proportional refund',
                  value: {
                    webhookData: {
                      id: *************,
                      order_id: **************,
                      refund_line_items: [
                        {
                          id: *************,
                          quantity: 1,
                          subtotal: 197.15,
                          total_tax: 35.49,
                          line_item: {
                            id: **************,
                            variant_id: **************,
                            title: 'Miniklub Baby Multi Color Roly Poly Bunny Soft Rattle Toy',
                            price: '300.00'
                          }
                        }
                      ],
                      transactions: [
                        {
                          gateway: 'Cash on Delivery (COD)',
                          amount: '232.64'
                        }
                      ]
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'Store credit successfully credited to customer account',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: {
                      type: 'string',
                      example: 'Store credit refund processed successfully'
                    },
                    transaction: {
                      type: 'object',
                      properties: {
                        id: { type: 'integer', example: 9246118 },
                        shopifyCustomerId: { type: 'string', example: '**************' },
                        transactionType: { type: 'string', example: 'API Manual' },
                        effectiveAt: { type: 'string', format: 'date-time' },
                        customerOpenBalance: { type: 'string', example: '4187.9700' },
                        amount: { type: 'string', example: '275.5000' },
                        amountCurrency: { type: 'string', example: 'INR' },
                        memo: { type: 'string', example: 'COD Refund - Order #1066' }
                      }
                    },
                    refundDetails: {
                      type: 'object',
                      properties: {
                        refundAmount: { type: 'string', example: '275.50' },
                        isCOD: { type: 'boolean', example: true },
                        proportionalCalculation: { type: 'boolean', example: true }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: 'Bad request - Missing required parameters',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Missing required parameters' }
                  }
                }
              }
            }
          },
          401: {
            description: 'Unauthorized - Missing or invalid authorization token',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Missing Authorization token in headers' }
                  }
                }
              }
            }
          },
          500: {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'Failed to credit store credit to customer' }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
};

const options = {
  swaggerDefinition,
  apis: [] // No external files, everything is defined above
};

const consolidatedSwaggerSpec = swaggerJSDoc(options);
export default consolidatedSwaggerSpec;
