# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "06af543b44d2ee54bc279560159477a1"
name = "MiniKlub-Store-Credit-App"
handle = "miniklub-store-credit-app"
application_url = "https://acquired-licking-verde-desired.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "checkout-functions-test.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_files,write_customers,write_files,write_products,read_orders"

[auth]
redirect_urls = ["https://acquired-licking-verde-desired.trycloudflare.com/auth/callback", "https://acquired-licking-verde-desired.trycloudflare.com/auth/shopify/callback", "https://acquired-licking-verde-desired.trycloudflare.com/api/auth/callback"]

[webhooks]
api_version = "2025-04"

[app_proxy]
url = "https://acquired-licking-verde-desired.trycloudflare.com"
subpath = "mk"
prefix = "apps"

[pos]
embedded = false
