import express from "express";
import { verifyProxy } from "../middlewares/proxyVerify.js";
import { editCustomerProfile } from "../controllers/customerController.js";
import { validateRequest } from "../middlewares/validations.js";
import { customerUpdateSchema } from "../utils/validationSchemas/customerProfileSchema.js";
const CustomerRouter = express.Router();

CustomerRouter.post(
  "/edit",
  verifyProxy,
  validateRequest(customerUpdateSchema),
  editCustomerProfile
);

export default CustomerRouter;
