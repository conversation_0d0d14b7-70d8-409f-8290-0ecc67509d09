import AppError from "../middlewares/AppError.js";
import shopify from "../shopify.js";
import { STAGED_UPLOADS_CREATE_MUTATION } from "./graphQlQueries.js";
import multer from "multer";

export const validateImage = (file) => {
  if (!file.mimetype.startsWith("image/")) {
    throw new AppError("Unsupported image format", 400, {
      errors: [{ field: "image", message: "Unsupported image format" }],
    });
  }

  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    throw new AppError("File too large", 400, {
      errors: [{ field: "image", message: "Image must be under 5MB" }],
    });
  }
};

export const uploadImage = async (shop, accessToken, file) => {
  try {
    // Get the signed URL from Shopify
    const { data: stagedData } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken },
    }).request(STAGED_UPLOADS_CREATE_MUTATION, {
      variables: {
        input: [
          {
            filename: file.originalname,
            mimeType: file.mimetype,
            httpMethod: "POST",
            resource: "FILE",
          },
        ],
      },
    });

    if (stagedData.stagedUploadsCreate.userErrors?.length > 0) {
      console.error(
        "Shopify returned user errors:",
        stagedData.stagedUploadsCreate.userErrors
      );
      throw new AppError("Image upload failed", 400, {
        errors: stagedData.stagedUploadsCreate.userErrors.map((err) => ({
          field: err.field || "image",
          message: err.message,
        })),
      });
    }

    const stagedTarget = stagedData.stagedUploadsCreate.stagedTargets[0];
    if (!stagedTarget) {
      console.error("No staged target returned from Shopify");
      throw new AppError("Failed to get upload URL", 500, {
        errors: [{ field: "image", message: "No upload URL returned" }],
      });
    }

    const { url, resourceUrl, parameters } = stagedTarget;

    // Create FormData and append all parameters
    const formData = new FormData();
    parameters.forEach(({ name, value }) => {
      formData.append(name, value);
    });

    // Create a Blob from the buffer
    const fileBlob = new Blob([file.buffer], { type: file.mimetype });
    formData.append("file", fileBlob, file.originalname);

    // Upload to the signed URL
    const uploadResponse = await fetch(url, {
      method: "POST",
      body: formData,
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error("Upload failed:", errorText);
      throw new AppError("Failed to upload file", 500, {
        errors: [
          {
            field: "image",
            message: `Upload failed with status ${uploadResponse.status}: ${errorText}`,
          },
        ],
      });
    }

    console.log("Upload successful! Resource URL:", resourceUrl);
    return resourceUrl;
  } catch (error) {
    console.error("Error in uploadImage:", {
      error: error.message,
      stack: error.stack,
    });
    throw new AppError("Image upload failed. Try again.", 500, {
      errors: [
        { field: "image", message: `Image upload failed: ${error.message}` },
      ],
    });
  }
};

export const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
});

// Middleware to handle multiple files
export const handleMultipleFiles = upload.fields([
  { name: "image_child_1", maxCount: 1 },
  { name: "image_child_2", maxCount: 1 },
  { name: "image_child_3", maxCount: 1 },
  { name: "image_child_4", maxCount: 1 },
  { name: "image_child_5", maxCount: 1 },
]);

// Wrap the multer middleware in a promise
export const handleUpload = (req, res) => {
  return new Promise((resolve, reject) => {
    handleMultipleFiles(req, res, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
};
