{"name": "mini-klub-store-credit-app", "version": "1.0.0", "main": "web/index.js", "license": "UNLICENSED", "scripts": {"shopify": "shopify", "build": "shopify app build", "dev": "shopify app dev", "info": "shopify app info", "generate": "shopify app generate", "deploy": "shopify app deploy"}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "crypto": "^1.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "validator": "^13.15.15"}, "author": "pranavstc", "private": true, "workspaces": ["extensions/*", "web", "web/frontend"]}