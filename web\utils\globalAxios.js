import axios from "axios";

/**
 * Global axios request function with standardized configuration
 * @param {Object} config - Axios configuration object
 * @param {string} config.method - HTTP method (get, post, put, delete, etc.)
 * @param {string} config.url - Request URL
 * @param {Object} [config.headers] - Request headers
 * @param {Object} [config.data] - Request body data (for POST, PUT, etc.)
 * @param {Object} [config.params] - URL parameters
 * @param {number} [config.timeout] - Request timeout in milliseconds
 * @param {number} [config.maxBodyLength] - Maximum body length
 * @returns {Promise} Axios response promise
 */
export const globalAxiosRequest = async (config) => {
  try {
    // Default configuration
    const defaultConfig = {
      method: "get",
      maxBodyLength: Infinity,
      timeout: 30000, // 30 seconds default timeout
      headers: {
        "Content-Type": "application/json",
      },
    };

    // Merge default config with provided config
    const finalConfig = {
      ...defaultConfig,
      ...config,
      headers: {
        ...defaultConfig.headers,
        ...config.headers,
      },
    };

    console.log(`[Global Axios] ${finalConfig.method.toUpperCase()} ${finalConfig.url}`);
    if (finalConfig.data) {
      console.log(`[Global Axios] Request data:`, finalConfig.data);
    }
    if (finalConfig.headers) {
      console.log(`[Global Axios] Request headers:`, finalConfig.headers);
    }

    const response = await axios.request(finalConfig);
    
    console.log(`[Global Axios] Response status: ${response.status}`);
    console.log(`[Global Axios] Response data:`, response.data);

    return response;
  } catch (error) {
    console.error(`[Global Axios] Error:`, error.response?.data || error.message);
    throw error;
  }
};

/**
 * Convenience function for GET requests
 * @param {string} url - Request URL
 * @param {Object} [options] - Additional options (headers, params, etc.)
 * @returns {Promise} Axios response promise
 */
export const globalGet = async (url, options = {}) => {
  return globalAxiosRequest({
    method: "get",
    url,
    ...options,
  });
};

/**
 * Convenience function for POST requests
 * @param {string} url - Request URL
 * @param {Object} [data] - Request body data
 * @param {Object} [options] - Additional options (headers, params, etc.)
 * @returns {Promise} Axios response promise
 */
export const globalPost = async (url, data = null, options = {}) => {
  return globalAxiosRequest({
    method: "post",
    url,
    data,
    ...options,
  });
};

/**
 * Convenience function for PUT requests
 * @param {string} url - Request URL
 * @param {Object} [data] - Request body data
 * @param {Object} [options] - Additional options (headers, params, etc.)
 * @returns {Promise} Axios response promise
 */
export const globalPut = async (url, data = null, options = {}) => {
  return globalAxiosRequest({
    method: "put",
    url,
    data,
    ...options,
  });
};

/**
 * Convenience function for DELETE requests
 * @param {string} url - Request URL
 * @param {Object} [options] - Additional options (headers, params, etc.)
 * @returns {Promise} Axios response promise
 */
export const globalDelete = async (url, options = {}) => {
  return globalAxiosRequest({
    method: "delete",
    url,
    ...options,
  });
};

/**
 * Helper function to build Rewardify API URLs
 * @param {string} endpoint - API endpoint (without base URL)
 * @returns {string} Complete URL
 */
export const buildRewardifyUrl = (endpoint) => {
  const baseUrl = process.env.REWARDIFY_API_BASE_URL;
  if (!baseUrl) {
    throw new Error("REWARDIFY_API_BASE_URL environment variable is not set");
  }
  // Remove leading slash from endpoint if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  // Ensure base URL ends with slash
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
  return `${cleanBaseUrl}${cleanEndpoint}`;
};

/**
 * Helper function to build QwikCilver API URLs
 * @param {string} endpoint - API endpoint (without base URL)
 * @returns {string} Complete URL
 */
export const buildQwikCilverUrl = (endpoint) => {
  const baseUrl = process.env.QWIKCILVER_API_BASE_URL;
  if (!baseUrl) {
    throw new Error("QWIKCILVER_API_BASE_URL environment variable is not set");
  }
  // Remove leading slash from endpoint if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  // Ensure base URL ends with slash
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
  return `${cleanBaseUrl}${cleanEndpoint}`;
};
