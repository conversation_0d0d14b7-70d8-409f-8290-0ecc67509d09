import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { creditCustomerInRewardify, getRewardifyOrders } from "../services/rewardifyService.js";
import { rewardifyAuth, creditRewardifyStoreCredit, checkRewardifyCustomer } from "../services/storeCreditService.js";
import { getATFromSQL } from "../middlewares/helpers.js";
import { extractAuthToken, createApiError, validateRewardifyData } from "../utils/storeCreditHelpers.js";
import shopify from "../shopify.js";

const GET_ORDER_QUERY = `
  query getOrder($orderId: ID!) {
    order(id: $orderId) {
      id
      name
      createdAt
      cancelledAt
      cancelReason
      confirmed

      totalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      subtotalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalTaxSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalShippingPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }

      customer {
        id
        email
      }
      email

      lineItems(first: 50) {
        edges {
          node {
            title
            quantity
            variant {
              id
              title
              sku
              price
            }
            discountedUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
      }

      transactions {
        id
        kind
        status
        gateway
        amountSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        createdAt
      }

      discountApplications(first: 10) {
        edges {
          node {
           __typename
            ... on DiscountCodeApplication {
              code
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
            ... on ManualDiscountApplication {
              title
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
          }
        }
      }
    }
  }
`;

//#################### REWARDIFY AUTH ####################

export const rewardifyAuthController = catchAsync(async (_req, res) => {
  const result = await rewardifyAuth();
  return new AppSuccess(res, result);
});

//#################### FETCH REWARDIFY TRANSACTIONS ####################


export const fetchRewardifyTransactions = catchAsync(async (req, res) => {
  const { orderId } = req.params;
  const token = extractAuthToken(req.headers);

  if (!orderId) {
    throw new AppError("Missing orderId in URL params", 400, {
      errors: [{ field: "orderId", message: "orderId is required in URL params" }]
    });
  }

  try {
    const response = await getRewardifyOrders(token, orderId);
    const transactions = Array.isArray(response) ? response : [];
    const foundDiscountRedemption = transactions.some(
      (tx) => tx.transactionType === "Discount Redemption"
    );

    return new AppSuccess(res, {
      orderId,
      foundDiscountRedemption,
      totalTransactions: transactions.length,
      transactions,
    });
  } catch (error) {
    throw createApiError("Failed to fetch Rewardify transactions", error, "rewardify");
  }
});


//#################### CREDIT REWARDIFY STORE CREDIT ####################

export const creditRewardifyStoreCreditController = catchAsync(async (req, res) => {
  const rewardifyToken = extractAuthToken(req.headers);
  const { customerId } = req.params;
  const { email, amount, memo, expiresAt, sendEmail, emailNote } = req.body;

  if (!customerId || !email || !amount || !memo || sendEmail === undefined || !emailNote) {
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "params/body",
          message: "customerId (in URL), and email, amount, memo, sendEmail, emailNote (in body) are required",
        },
      ],
    });
  }

  try {
    // Check if customer exists in Rewardify
    try {
      await checkRewardifyCustomer(rewardifyToken, customerId);
    } catch (customerError) {
      if (customerError.response?.status === 404) {
        throw new AppError("Customer not found in Rewardify", 404, {
          errors: [
            {
              field: "customerId",
              message: `Customer ID ${customerId} does not exist in Rewardify system`,
            },
          ],
        });
      }
    }

    const result = await creditRewardifyStoreCredit({
      rewardifyToken,
      customerId,
      email,
      amount,
      memo,
      expiresAt,
      sendEmail,
      emailNote
    });

    return new AppSuccess(res, result);
  } catch (error) {
    if (error instanceof AppError) throw error;
    throw createApiError("Failed to credit Rewardify store credit", error, "rewardify");
  }
});

//#################### CREDIT STORE CREDIT TO CUSTOMER ####################

// Service function to credit store credit to customer (can be called directly)
export const creditStoreCreditToCustomerService = async (shopifyCustomerId, token, requestData) => {
  if (requestData.webhookData) {
    try {
      // Call proportionate amount calculation function
      const calculationResult = await processProportionalStoreCredit(requestData.webhookData);

      if (!calculationResult.shouldCredit) {
        return {
          message: calculationResult.message,
          refundAmount: calculationResult.refundAmount,
          processed: false
        };
      }

      // Credit the proportional amount to customer account
      const { customerData, refundAmount } = calculationResult;

      const response = await creditCustomerInRewardify({
        token,
        shopifyCustomerId: customerData.customerId,
        customerEmail: customerData.customerEmail,
        amount: refundAmount,
        itemTitle: `${calculationResult.isCOD ? 'COD' : 'Store Credit'} Refund - Order #${customerData.orderName}`,
        orderId: customerData.orderId,
      });

      const transaction = response.data?.transaction || {
        transactionType: "API Manual",
        amount: parseFloat(refundAmount).toFixed(4),
        amountCurrency: "INR"
      };

      return {
        message: "Proportional store credit refund processed successfully",
        refundAmount,
        refundDetails: calculationResult.refundDetails,
        transaction,
        processed: true
      };

    } catch (error) {
      throw createApiError("Failed to process proportional refund", error, "rewardify");
    }
  }

  // Regular manual credit request (existing functionality)
  const { email, amount, memo } = requestData;

  try {
    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we'll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    return { transaction };
  } catch (error) {
    throw createApiError("Failed to credit customer", error, "rewardify");
  }
};

// Express controller wrapper for the service function
export const creditStoreCreditToCustomer = catchAsync(async (req, res) => {
  const shopifyCustomerId = req.params.shopifyCustomerId;
  const token = extractAuthToken(req.headers);

  if (req.body.webhookData) {
    try {
      // Call proportionate amount calculation function
      const calculationResult = await processProportionalStoreCredit(req.body.webhookData);

      if (!calculationResult.shouldCredit) {
        return new AppSuccess(res, {
          message: calculationResult.message,
          refundAmount: calculationResult.refundAmount,
          processed: false
        });
      }

      // Credit the proportional amount to customer account
      const { customerData, refundAmount } = calculationResult;

      const response = await creditCustomerInRewardify({
        token,
        shopifyCustomerId: customerData.customerId,
        customerEmail: customerData.customerEmail,
        amount: refundAmount,
        itemTitle: `${calculationResult.isCOD ? 'COD' : 'Store Credit'} Refund - Order #${customerData.orderName}`,
        orderId: customerData.orderId,
      });

      const transaction = response.data?.transaction || {
        transactionType: "API Manual",
        amount: parseFloat(refundAmount).toFixed(4),
        amountCurrency: "INR"
      };
      return new AppSuccess(res, {
        message: "Proportional store credit refund processed successfully",
        refundAmount,
        refundDetails: calculationResult.refundDetails,
        transaction,
        processed: true
      });

    } catch (error) {
      throw createApiError("Failed to process proportional refund", error, "rewardify");
    }
  }

  // Regular manual credit request (existing functionality)
  const { email, amount, memo } = req.body;

  try {
    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we’ll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    return new AppSuccess(res, { transaction });
  } catch (error) {
    throw createApiError("Failed to credit customer", error, "rewardify");
  }
});


// Service function to fetch Shopify order data (can be called directly)
export const getShopifyOrderData = async (orderId) => {
  if (!orderId) {
    throw new AppError("Missing orderId parameter", 400, {
      errors: [{
        field: "orderId",
        message: "orderId is required",
        example: "getShopifyOrderData('12345')"
      }]
    });
  }

  // Convert orderId to string if it's a number
  const orderIdString = String(orderId);
  console.log("Processing orderId:", orderIdString, "Type:", typeof orderIdString);

  try {
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      throw new AppError("Authentication failed", 401, {
        errors: [{
          field: "accessToken",
          message: "Access token not found for the specified shop",
          debug: {
            shopFromEnv: shop,
            availableShops: storeData?.map(x => x.shop) || [],
            storeDataCount: storeData?.length || 0
          }
        }]
      });
    }
    const client = new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT }
    });
    const shopifyOrderId = orderIdString.startsWith('gid://')
      ? orderIdString
      : `gid://shopify/Order/${orderIdString}`;
    const { data } = await client.request(GET_ORDER_QUERY, {
      variables: { orderId: shopifyOrderId }
    });

    console.log("Order details" , data)

    if (!data.order) {
      throw new AppError("Order not found", 404, {
        errors: [{
          field: "orderId",
          message: "Order may not exist or you may not have permission to access it",
          orderId: orderIdString,
          shopifyOrderId
        }]
      });
    }

    return {
      message: "Shopify order fetched successfully",
      orderId: orderIdString,
      shopifyOrderId,
      orderData: data.order,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    if (error instanceof AppError) throw error;

    console.error("❌ Error fetching Shopify order:", error);
    console.error("❌ Error details:", {
      message: error.message,
      response: error?.response?.data,
      body: error?.body,
      graphQLErrors: error?.body?.errors?.graphQLErrors
    });

    // Handle different types of errors with enhanced error details
    if (error.message?.includes("401 Unauthorized")) {
      throw new AppError("Unauthorized access to Shopify API", 401, {
        errors: [{
          field: "authentication",
          message: "Unauthorized access to Shopify API",
          suggestions: [
            "Check if the Shopify app is properly installed",
            "Verify that the access token is valid",
            "Ensure the app has 'read_orders' scope",
            "Try reinstalling the Shopify app"
          ]
        }]
      });
    } else if (error.message?.includes("404")) {
      throw new AppError("Order not found", 404, {
        errors: [{
          field: "orderId",
          message: "Order not found",
          suggestions: [
            "Verify the order ID is correct",
            "Check if the order exists in your Shopify store",
            "Ensure you have permission to access this order"
          ]
        }]
      });
    } else if (error?.body?.errors?.graphQLErrors) {
      const graphQLError = error.body.errors.graphQLErrors[0]?.message || "GraphQL error";
      throw new AppError("GraphQL error", 400, {
        errors: [{
          field: "graphql",
          message: graphQLError,
          suggestions: ["Check the GraphQL query syntax and permissions"]
        }]
      });
    }

    throw createApiError("Failed to fetch Shopify order", error, "shopify");
  }
};

// Express controller wrapper for the service function
export const fetchRewardifyOrder = catchAsync(async (req, res) => {
  const { orderId } = req.params;
  const result = await getShopifyOrderData(orderId);
  return new AppSuccess(res, result);
});

// Process proportional store credit refund calculation
export const processProportionalStoreCredit = async (webhookData) => {
  try {
    const orderId = webhookData.order_id;
    const refundedLineItems = webhookData.refund_line_items || [];
    const customerEmail = webhookData.email;
    const refundTransactions = webhookData.transactions || [];
    if (!orderId || !refundedLineItems.length) {
      throw new Error("Missing required webhook data: orderId or refundedLineItems");
    }
    const gateway = refundTransactions.length > 0 ? refundTransactions[0].gateway : null;
    const isCOD = gateway && gateway.toLowerCase().includes('cash on delivery');
    const authResponse = await rewardifyAuth()
    const accessToken = authResponse.access_token;

    if (!accessToken) {
      throw new Error("No access token received from auth API");
    }
    // Call the service function directly instead of making HTTP request
    const transactions = await getRewardifyOrders(accessToken, orderId);
    const foundDiscountRedemption = transactions.some(
      (tx) => tx.transactionType === "Discount Redemption"
    );

    const transactionsData = {
      data: {
        orderId,
        foundDiscountRedemption,
        totalTransactions: transactions.length,
        transactions,
      }
    };
    let storeCreditUsedAmount = 0;
    let transactionCustomerId = null;

    // Call the service function directly instead of making HTTP request
    const orderResponse = await getShopifyOrderData(orderId);
    const order = orderResponse.orderData;

  if (isCOD) {
  transactionCustomerId = order?.customer?.id?.replace("gid://shopify/Customer/", "");

  // Check if store credit was used in this COD order
  if (transactionsData.data.foundDiscountRedemption) {
    for (const transaction of transactionsData.data.transactions) {
      if (transaction.transactionType === "Discount Redemption" && transaction.amount) {
        storeCreditUsedAmount += Math.abs(parseFloat(transaction.amount));
      }
    }
  } else {
    storeCreditUsedAmount = 0;
  }
} else if (transactionsData.data.foundDiscountRedemption) {
  for (const transaction of transactionsData.data.transactions) {
    if (transaction.transactionType === "Discount Redemption" && transaction.amount) {
      storeCreditUsedAmount += Math.abs(parseFloat(transaction.amount));
      if (transaction.shopifyCustomerId && !transactionCustomerId) {
        transactionCustomerId = transaction.shopifyCustomerId;
      }
    }
  }
} else {
  return {
    success: true,
    message: "No store credit refund needed - order didn't use store credit",
    refundAmount: 0,
    shouldCredit: false,
    accessToken: accessToken
  };
}
    if (!transactionCustomerId) {
      throw new Error("No shopifyCustomerId found in transactions data");
    }
    const finalCustomerId = transactionCustomerId;

    // Step 4: Get price of each product and calculate total order subtotal
    let totalOrderSubtotal = 0;
    const productPrices = {};

    for (const lineItemEdge of order.lineItems.edges) {
      const lineItem = lineItemEdge.node;
      const productPrice = parseFloat(lineItem.variant.price) * lineItem.quantity;
      totalOrderSubtotal += productPrice;
      productPrices[lineItem.variant.id] = {
        unitPrice: parseFloat(lineItem.variant.price),
        quantity: lineItem.quantity,
        totalPrice: productPrice,
        title: lineItem.title
      };
    }

    let totalRefundAmount = 0;

    const refundDetails = [];

    if (isCOD) {
      // For COD: Always refund subtotal + tax, plus proportional store credit if store credit was used
      let totalSubtotal = 0;
      let totalTax = 0;
      let totalProportionalStoreCredit = 0;

      // Process each refunded item
      for (const refundedItem of refundedLineItems) {
        const { subtotal, total_tax, line_item } = refundedItem;
        const webhookVariantId = line_item?.variant_id;

        totalSubtotal += (subtotal || 0);
        totalTax += (total_tax || 0);

        // Calculate proportional store credit for this specific product (only if store credit was used)
        let proportionalStoreCredit = 0;
        if (webhookVariantId && storeCreditUsedAmount > 0) {
          const graphqlVariantId = `gid://shopify/ProductVariant/${webhookVariantId}`;
          const productInfo = productPrices[graphqlVariantId];

          if (productInfo) {
            // Calculate proportion: (product price / total order value) * total store credit used
            const productProportion = productInfo.totalPrice / totalOrderSubtotal;
            proportionalStoreCredit = storeCreditUsedAmount * productProportion;
            totalProportionalStoreCredit += proportionalStoreCredit;
          } else {
            console.log(`COD Refunded: ${line_item?.title} - Subtotal: ₹${subtotal}, Tax: ₹${total_tax}`);
            console.log(`- Could not find product info for proportional store credit calculation`);
          }
        } else {
          if (storeCreditUsedAmount === 0) {
            console.log(`   - No store credit was used in this order`);
          }
        }

        refundDetails.push({
          productTitle: line_item?.title || "Unknown Product",
          variantId: webhookVariantId || "unknown",
          subtotal: subtotal || 0,
          tax: total_tax || 0,
          proportionalStoreCredit: proportionalStoreCredit.toFixed(2),
          refundType: storeCreditUsedAmount > 0 ? "COD Refund: Subtotal + Tax + Proportional Store Credit" : "COD Refund: Subtotal + Tax Only"
        });
      }

      // Calculate total refund: subtotal + tax + proportional store credit (if any)
      const webhookRefundAmount = totalSubtotal + totalTax;
      totalRefundAmount = webhookRefundAmount + totalProportionalStoreCredit;
      if (storeCreditUsedAmount > 0) {
        console.log(`💰 COD Proportional store credit: ₹${totalProportionalStoreCredit.toFixed(2)}`);
        console.log(`💰 COD Total refund amount (subtotal + tax + store credit): ₹${totalRefundAmount.toFixed(2)}`);
      } else {
        console.log(`💰 COD Total refund amount (subtotal + tax only): ₹${totalRefundAmount.toFixed(2)}`);
      }

    } else {
      console.log("💳 Non-COD Gateway: Refunding only store credit used");

      // For non-COD: Only refund store credit used
      if (!transactionsData.data?.foundDiscountRedemption || storeCreditUsedAmount === 0) {
        return {
          success: true,
          message: "No store credit refund needed - order didn't use store credit",
          refundAmount: 0,
          shouldCredit: false
        };
      }

      // Calculate proportional store credit refund for each item
      for (const refundedItem of refundedLineItems) {
        const { quantity, line_item } = refundedItem;
        // Get variant_id from line_item in webhook
        const webhookVariantId = line_item?.variant_id;
        if (!webhookVariantId) {
          console.log(`⚠️ No variant_id found in refunded item:`, refundedItem);
          continue;
        }
        const graphqlVariantId = `gid://shopify/ProductVariant/${webhookVariantId}`;
        const productInfo = productPrices[graphqlVariantId];

        if (!productInfo) {
          console.log(`⚠️ Could not find product info for variant_id: ${graphqlVariantId}`);
          console.log(`Available variants:`, Object.keys(productPrices));
          continue;
        }
        const variantId = graphqlVariantId;

        // Calculate proportion of store credit for this product
        const productProportion = productInfo.totalPrice / totalOrderSubtotal;
        const productStoreCreditAmount = storeCreditUsedAmount * productProportion;

        // Calculate refund proportion based on quantity refunded
        const refundProportion = quantity / productInfo.quantity;
        const refundStoreCreditAmount = productStoreCreditAmount * refundProportion;

        totalRefundAmount += refundStoreCreditAmount;

        refundDetails.push({
          productTitle: productInfo.title,
          variantId,
          originalPrice: productInfo.totalPrice,
          refundedQuantity: quantity,
          totalQuantity: productInfo.quantity,
          productProportion: productProportion.toFixed(4),
          productStoreCreditAmount: productStoreCreditAmount.toFixed(2),
          refundProportion: refundProportion.toFixed(4),
          refundStoreCreditAmount: refundStoreCreditAmount.toFixed(2),
          refundType: "Store Credit Only (Non-COD)"
        });
      }
    }
    // Return calculation results for crediting
    return {
      success: true,
      message: "Proportional calculation completed",
      refundAmount: totalRefundAmount.toFixed(2),
      storeCreditUsedAmount: storeCreditUsedAmount.toFixed(2),
      foundDiscountRedemption: transactionsData.foundDiscountRedemption,
      refundDetails,
      shouldCredit: totalRefundAmount > 0,
      gateway,
      isCOD,
      accessToken: accessToken, // Include access token for credit API
      customerData: {
        customerId: finalCustomerId,
        customerEmail,
        orderId,
        orderName: order.name
      }
    };

  } catch (error) {
    console.error(" Error processing proportional store credit calculation:", error);
    throw error;
  }
};

export const webhookHandler = async (req, res) => {
  try {
    const webhookData = req.body;
    const orderId = webhookData.order_id;

    if (!orderId) {
      return res.status(200).send("Refund webhook processed - no order ID");
    }
    try {
      const refundResult = await processProportionalStoreCredit(webhookData);

      if (refundResult.shouldCredit) {
        // Get customer data and access token from the refund result
        const { customerId, customerEmail, orderName } = refundResult.customerData;
        const refundAmount = refundResult.refundAmount;
        const accessToken = refundResult.accessToken;

        // Prepare credit data
        const creditData = {
          email: customerEmail,
          memo: `Store credit refund for order ${orderName} - Proportional refund`,
          expiresAt: null, // No expiry
          sendEmail: true,
          emailNote: `Your store credit refund of ₹${refundAmount} for order ${orderName} has been processed.`,
          amount: refundAmount
        };

        try {
          // Call the service function directly instead of making HTTP request
          const creditResult = await creditStoreCreditToCustomerService(customerId, accessToken, creditData);
          console.log(" Store credit refund processing completed:", creditResult);
          res.status(200).send("Refund webhook processed with store credit refund");
        } catch (error) {
          console.error("❌ Failed to credit customer:", error);
          res.status(200).send("Refund webhook processed - credit failed");
        }
      } else {
        res.status(200).send("Refund webhook processed - no store credit refund needed");
      }

    } catch (storeCreditError) {
      res.status(200).send("Refund webhook processed - store credit refund failed");
    }

  } catch (err) {
    res.status(500).send("Internal error");
  }
};




 