import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { creditCustomerInRewardify, getRewardifyOrders, orderCancelStoreCredit, processProportionalStoreCredit } from "../services/rewardifyService.js";
import { rewardifyAuth, creditRewardifyStoreCredit, checkRewardifyCustomer } from "../services/storeCreditService.js";
import { getATFromSQL } from "../middlewares/helpers.js";
import { extractAuthToken, createApiError, validateRewardifyData } from "../utils/storeCreditHelpers.js";
import shopify from "../shopify.js";

const GET_ORDER_QUERY = `
  query getOrder($orderId: ID!) {
    order(id: $orderId) {
      id
      name
      createdAt
      cancelledAt
      cancelReason
      confirmed

      totalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      subtotalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalTaxSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalShippingPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }

      customer {
        id
        email
      }
      email

      lineItems(first: 50) {
        edges {
          node {
            title
            quantity
            variant {
              id
              title
              sku
              price
            }
            discountedUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
      }

      transactions {
        id
        kind
        status
        gateway
        amountSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        createdAt
      }

      discountApplications(first: 10) {
        edges {
          node {
           __typename
            ... on DiscountCodeApplication {
              code
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
            ... on ManualDiscountApplication {
              title
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
          }
        }
      }
    }
  }
`;

//#################### REWARDIFY AUTH ####################

export const rewardifyAuthController = catchAsync(async (_req, res) => {
  const result = await rewardifyAuth();
  return new AppSuccess(res, result);
});

//#################### FETCH REWARDIFY TRANSACTIONS ####################


export const fetchRewardifyTransactions = catchAsync(async (req, res) => {
  const { orderId } = req.params;
  const token = extractAuthToken(req.headers);

  if (!orderId) {
    throw new AppError("Missing orderId in URL params", 400, {
      errors: [{ field: "orderId", message: "orderId is required in URL params" }]
    });
  }

  try {
    const response = await getRewardifyOrders(token, orderId);
    const transactions = Array.isArray(response) ? response : [];
    const foundDiscountRedemption = transactions.some(
      (tx) => tx.transactionType === "Discount Redemption"
    );

    return new AppSuccess(res, {
      orderId,
      foundDiscountRedemption,
      totalTransactions: transactions.length,
      transactions,
    });
  } catch (error) {
    throw createApiError("Failed to fetch Rewardify transactions", error, "rewardify");
  }
});


//#################### CREDIT REWARDIFY STORE CREDIT ####################

export const creditRewardifyStoreCreditController = catchAsync(async (req, res) => {
  const rewardifyToken = extractAuthToken(req.headers);
  const { customerId } = req.params;
  const { email, amount, memo, expiresAt, sendEmail, emailNote } = req.body;

  if (!customerId || !email || !amount || !memo || sendEmail === undefined || !emailNote) {
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "params/body",
          message: "customerId (in URL), and email, amount, memo, sendEmail, emailNote (in body) are required",
        },
      ],
    });
  }

  try {
    // Check if customer exists in Rewardify
    try {
      await checkRewardifyCustomer(rewardifyToken, customerId);
    } catch (customerError) {
      if (customerError.response?.status === 404) {
        throw new AppError("Customer not found in Rewardify", 404, {
          errors: [
            {
              field: "customerId",
              message: `Customer ID ${customerId} does not exist in Rewardify system`,
            },
          ],
        });
      }
    }

    const result = await creditRewardifyStoreCredit({
      rewardifyToken,
      customerId,
      email,
      amount,
      memo,
      expiresAt,
      sendEmail,
      emailNote
    });

    return new AppSuccess(res, result);
  } catch (error) {
    if (error instanceof AppError) throw error;
    throw createApiError("Failed to credit Rewardify store credit", error, "rewardify");
  }
});

//#################### CREDIT STORE CREDIT TO CUSTOMER ####################

// Service function to credit store credit to customer (can be called directly)
export const creditStoreCreditToCustomerService = async (shopifyCustomerId, token, requestData) => {
  if (requestData.webhookData) {
    try {
      // Call proportionate amount calculation function
      const calculationResult = await processProportionalStoreCredit(requestData.webhookData);

      if (!calculationResult.shouldCredit) {
        return {
          message: calculationResult.message,
          refundAmount: calculationResult.refundAmount,
          processed: false
        };
      }

      // Credit the proportional amount to customer account
      const { customerData, refundAmount } = calculationResult;

      const response = await creditCustomerInRewardify({
        token,
        shopifyCustomerId: customerData.customerId,
        customerEmail: customerData.customerEmail,
        amount: refundAmount,
        itemTitle: `${calculationResult.isCOD ? 'COD' : 'Store Credit'} Refund - Order #${customerData.orderName}`,
        orderId: customerData.orderId,
      });

      const transaction = response.data?.transaction || {
        transactionType: "API Manual",
        amount: parseFloat(refundAmount).toFixed(4),
        amountCurrency: "INR"
      };

      return {
        message: "Proportional store credit refund processed successfully",
        refundAmount,
        refundDetails: calculationResult.refundDetails,
        transaction,
        processed: true
      };

    } catch (error) {
      throw createApiError("Failed to process proportional refund", error, "rewardify");
    }
  }

  // Regular manual credit request (existing functionality)
  const { email, amount, memo } = requestData;

  try {
    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we'll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    return { transaction };
  } catch (error) {
    throw createApiError("Failed to credit customer", error, "rewardify");
  }
};

// Express controller wrapper for the service function
export const creditStoreCreditToCustomer = catchAsync(async (req, res) => {
  const shopifyCustomerId = req.params.shopifyCustomerId;
  const token = extractAuthToken(req.headers);

  if (req.body.webhookData) {
    try {
      // Call proportionate amount calculation function
      const calculationResult = await processProportionalStoreCredit(req.body.webhookData);

      if (!calculationResult.shouldCredit) {
        return new AppSuccess(res, {
          message: calculationResult.message,
          refundAmount: calculationResult.refundAmount,
          processed: false
        });
      }

      // Credit the proportional amount to customer account
      const { customerData, refundAmount } = calculationResult;

      const response = await creditCustomerInRewardify({
        token,
        shopifyCustomerId: customerData.customerId,
        customerEmail: customerData.customerEmail,
        amount: refundAmount,
        itemTitle: `${calculationResult.isCOD ? 'COD' : 'Store Credit'} Refund - Order #${customerData.orderName}`,
        orderId: customerData.orderId,
      });

      const transaction = response.data?.transaction || {
        transactionType: "API Manual",
        amount: parseFloat(refundAmount).toFixed(4),
        amountCurrency: "INR"
      };
      return new AppSuccess(res, {
        message: "Proportional store credit refund processed successfully",
        refundAmount,
        refundDetails: calculationResult.refundDetails,
        transaction,
        processed: true
      });

    } catch (error) {
      throw createApiError("Failed to process proportional refund", error, "rewardify");
    }
  }

  // Regular manual credit request (existing functionality)
  const { email, amount, memo } = req.body;

  try {
    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we’ll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    return new AppSuccess(res, { transaction });
  } catch (error) {
    throw createApiError("Failed to credit customer", error, "rewardify");
  }
});


// Service function to fetch Shopify order data (can be called directly)
export const getShopifyOrderData = async (orderId) => {
  if (!orderId) {
    throw new AppError("Missing orderId parameter", 400, {
      errors: [{
        field: "orderId",
        message: "orderId is required",
        example: "getShopifyOrderData('12345')"
      }]
    });
  }

  // Convert orderId to string if it's a number
  const orderIdString = String(orderId);
  console.log("Processing orderId:", orderIdString, "Type:", typeof orderIdString);

  try {
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      throw new AppError("Authentication failed", 401, {
        errors: [{
          field: "accessToken",
          message: "Access token not found for the specified shop",
          debug: {
            shopFromEnv: shop,
            availableShops: storeData?.map(x => x.shop) || [],
            storeDataCount: storeData?.length || 0
          }
        }]
      });
    }
    const client = new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT }
    });
    const shopifyOrderId = orderIdString.startsWith('gid://')
      ? orderIdString
      : `gid://shopify/Order/${orderIdString}`;
    const { data } = await client.request(GET_ORDER_QUERY, {
      variables: { orderId: shopifyOrderId }
    });

    console.log("Order details" , data)

    if (!data.order) {
      throw new AppError("Order not found", 404, {
        errors: [{
          field: "orderId",
          message: "Order may not exist or you may not have permission to access it",
          orderId: orderIdString,
          shopifyOrderId
        }]
      });
    }

    return {
      message: "Shopify order fetched successfully",
      orderId: orderIdString,
      shopifyOrderId,
      orderData: data.order,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    if (error instanceof AppError) throw error;

    console.error("❌ Error fetching Shopify order:", error);
    console.error("❌ Error details:", {
      message: error.message,
      response: error?.response?.data,
      body: error?.body,
      graphQLErrors: error?.body?.errors?.graphQLErrors
    });

    // Handle different types of errors with enhanced error details
    if (error.message?.includes("401 Unauthorized")) {
      throw new AppError("Unauthorized access to Shopify API", 401, {
        errors: [{
          field: "authentication",
          message: "Unauthorized access to Shopify API",
          suggestions: [
            "Check if the Shopify app is properly installed",
            "Verify that the access token is valid",
            "Ensure the app has 'read_orders' scope",
            "Try reinstalling the Shopify app"
          ]
        }]
      });
    } else if (error.message?.includes("404")) {
      throw new AppError("Order not found", 404, {
        errors: [{
          field: "orderId",
          message: "Order not found",
          suggestions: [
            "Verify the order ID is correct",
            "Check if the order exists in your Shopify store",
            "Ensure you have permission to access this order"
          ]
        }]
      });
    } else if (error?.body?.errors?.graphQLErrors) {
      const graphQLError = error.body.errors.graphQLErrors[0]?.message || "GraphQL error";
      throw new AppError("GraphQL error", 400, {
        errors: [{
          field: "graphql",
          message: graphQLError,
          suggestions: ["Check the GraphQL query syntax and permissions"]
        }]
      });
    }

    throw createApiError("Failed to fetch Shopify order", error, "shopify");
  }
};

// Express controller wrapper for the service function
export const fetchRewardifyOrder = catchAsync(async (req, res) => {
  const { orderId } = req.params;
  const result = await getShopifyOrderData(orderId);
  return new AppSuccess(res, result);
});




// #################### REFUND WEBHOOK HANDLER ####################
export const webhookHandler = catchAsync(async (req, res) => {
  const webhookData = req.body;
  console.log("🔍 Webhook data keys:", Object.keys(webhookData));
  console.log("🔍 Refund line items:", webhookData.refund_line_items?.length || 0);
  console.log("🔍 Order adjustments:", webhookData.order_adjustments?.length || 0);

  const orderId = webhookData.order_id;
  if (!orderId) {
    return new AppSuccess(res, {
      message: "Refund webhook processed - no order ID",
      statusCode: 200,
    });
  }
  const isCancellation = webhookData.order_adjustments?.some(
    (adj) => adj.kind === "refund_discrepancy" || adj.reason === "Cancelled"
  );

  if (isCancellation) {
    return new AppSuccess(res, {
      message: "Refund webhook ignored - cancellation already processed",
      statusCode: 200,
    });
  }

  try {
    const refundResult = await processProportionalStoreCredit(webhookData);

    console.log("🔍 Refund result:", JSON.stringify(refundResult, null, 2));

    if (!refundResult || !refundResult.shouldCredit) {
      console.log("❌ No credit needed - Reason:", refundResult?.message || "Result is undefined");
      console.log("=== REFUND WEBHOOK END (NO CREDIT NEEDED) ===");
      return new AppSuccess(res, {
        message: "Refund webhook processed - no store credit refund needed",
        statusCode: 200,
      });
    }

    const { customerId, customerEmail, orderName } = refundResult.customerData;
    const refundAmount = refundResult.refundAmount;
    const accessToken = refundResult.accessToken;

    const creditData = {
      email: customerEmail,
      amount: refundAmount,
      memo: `Store credit refund for order ${orderName} - Proportional refund`,
      expiresAt: null,
      sendEmail: true,
      emailNote: `Your store credit refund of ₹${refundAmount} for order ${orderName} has been processed.`
    };

    const creditResult = await creditStoreCreditToCustomerService(
      customerId,
      accessToken,
      creditData
    );

    console.log("=== REFUND WEBHOOK END ===");
    return new AppSuccess(res, {
      message: "Refund webhook processed with store credit refund",
      data: {
        refundAmount,
        transaction: creditResult.transaction,
      },
      statusCode: 200,
    });
  } catch (error) {
    console.error("Error processing refund webhook:", error);
    console.log("=== REFUND WEBHOOK END (ERROR) ===");
    return new AppSuccess(res, {
      message: "Refund webhook processed - error occurred",
      statusCode: 200,
    });
  }
});


// #################### ORDER CANCELLED WEBHOOK ####################

export const orderCancelWebhookHandler = catchAsync(async (req, res) => {
  const webhookData = req.body;
  const orderId = webhookData.id;
  const customerEmail = webhookData?.customer?.email;
  if (!orderId) {
    throw new AppError("Cancel webhook missing order ID", 400);
  }
  const refundResult = await orderCancelStoreCredit(orderId, customerEmail);
  if (!refundResult.shouldCredit) {
    return new AppSuccess(res, {
      message: "Cancel webhook processed - no store credit refund needed",
    });
  }
  const { customerId, customerEmail: resolvedEmail } = refundResult.customerData;
  const refundAmount = refundResult.refundAmount;
  const accessToken = refundResult.accessToken;

  await creditCustomerInRewardify({
    token: accessToken,
    shopifyCustomerId: customerId,
    customerEmail: resolvedEmail,
    amount: refundAmount,
    itemTitle: "Order Cancellation - Store Credit Refund",
    orderId: orderId,
  });
  return new AppSuccess(res, {
    message: "Cancel webhook processed with store credit refund",
    data: {
      orderId,
      customerId,
      refundAmount,
    },
  });
});




 