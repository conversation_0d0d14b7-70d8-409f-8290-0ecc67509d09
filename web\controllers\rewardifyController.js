import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { creditCustomerInRewardify, creditStoreCreditToCustomerService, orderCancelStoreCredit, processProportionalStoreCredit } from "../services/rewardifyService.js";


// #################### REFUND WEBHOOK HANDLER ####################
export const refundWebhookHandler = catchAsync(async (req, res) => {
  const webhookData = req.body;
  const orderId = webhookData.order_id;
  if (!orderId) {
    return new AppSuccess(res, {
      message: "Refund webhook processed - no order ID",
      statusCode: 200,
    });
  }

  const isCancellation = webhookData.order_adjustments?.some(
    (adj) => adj.kind === "refund_discrepancy" || adj.reason === "Cancelled"
  );

  if (isCancellation) {
    return new AppSuccess(res, {
      message: "Refund webhook ignored - cancellation already processed",
      statusCode: 200,
    });
  }

  const refundResult = await processProportionalStoreCredit(webhookData);
  if (!refundResult || !refundResult.shouldCredit) {
    return new AppSuccess(res, {
      message: "Refund webhook processed - no store credit refund needed",
      statusCode: 200,
    });
  }

  const { customerId, customerEmail, orderName } = refundResult.customerData;
  const refundAmount = refundResult.refundAmount;
  const accessToken = refundResult.accessToken;
  if (!customerEmail) {
    console.error("❌ Customer email is missing - cannot credit store credit");
    return new AppSuccess(res, {
      message: "Refund webhook processed - customer email missing, cannot credit store credit",
      statusCode: 200,
    });
  }

  const creditData = {
    email: customerEmail,
    amount: refundAmount,
    memo: `Store credit refund for order ${orderName} - Proportional refund`,
    expiresAt: null,
    sendEmail: true,
    emailNote: `Your store credit refund of ₹${refundAmount} for order ${orderName} has been processed.`,
  };
  let creditResult;
  try {
    creditResult = await creditStoreCreditToCustomerService(
      customerId,
      accessToken,
      creditData
    );
  } catch (creditError) {
    console.error("❌ Error crediting store credit:", creditError);
    throw creditError;
  }

  return new AppSuccess(res, {
    message: "Refund webhook processed with store credit refund",
    data: {
      refundAmount,
      transaction: creditResult.transaction,
    },
    statusCode: 200,
  });
});



// #################### ORDER CANCELLED WEBHOOK ####################

export const orderCancelWebhookHandler = catchAsync(async (req, res) => {
  const webhookData = req.body;
  const orderId = webhookData.id;
  const customerEmail = webhookData?.customer?.email;
  
  if (!orderId) {
    throw new AppError("Cancel webhook missing order ID", 400, {
      errors: [{ field: "orderId", message: "Order ID is required" }]
    });
  }
  const refundResult = await orderCancelStoreCredit(orderId, customerEmail);
  if (!refundResult.shouldCredit) {
    return new AppSuccess(res, {
      message: "Cancel webhook processed - no store credit refund needed",
    });
  }
  const { customerId, customerEmail: resolvedEmail } = refundResult.customerData;
  const refundAmount = refundResult.refundAmount;
  const accessToken = refundResult.accessToken;

  await creditCustomerInRewardify({
    token: accessToken,
    shopifyCustomerId: customerId,
    customerEmail: resolvedEmail,
    amount: refundAmount,
    itemTitle: "Order Cancellation - Store Credit Refund",
    orderId: orderId,
  });
  return new AppSuccess(res, {
    message: "Cancel webhook processed with store credit refund",
    data: {
      orderId,
      customerId,
      refundAmount,
    },
  });
});




