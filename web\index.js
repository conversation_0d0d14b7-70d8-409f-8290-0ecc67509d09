// Load environment variables FIRST, before any other imports
import * as dotenv from "dotenv";
dotenv.config();

import express from "express";
import cors from "cors";
import serveStatic from "serve-static";
import swaggerUi from 'swagger-ui-express';
import shopify from "./shopify.js";
import productCreator from "./product-creator.js";
import PrivacyWebhookHandlers from "./privacy.js";
import globalErrorHandler from "./middlewares/globalErrorHandler.js";
import KidsRouter from "./routes/kidsRouter.js";
import NewsletterRouter from "./routes/newsletterRouter.js";
import StoreCreditRouter from "./routes/storeCreditRouter.js";
import WebhookRouter from "./routes/webhookRouter.js";
import indexRouter from "./routes/indexRouter.js";
import swaggerSpec from './swagger.js';
import { simpleSwaggerSpec } from './simple-swagger.js';
import consolidatedSwaggerSpec from './consolidated-swagger.js';

// Configuration
const PORT = parseInt(process.env.BACKEND_PORT || process.env.PORT || "3000", 10);
const STATIC_PATH = process.env.NODE_ENV === "production"
  ? `${process.cwd()}/frontend/dist`
  : `${process.cwd()}/frontend/`;

const app = express();

// Global middleware setup
app.set('trust proxy', true);
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'TransactionId', 'DateAtClient']
}));
app.use(express.json());

// Request logging middleware (only in development)
if (process.env.NODE_ENV !== "production") {
  app.use((req, _res, next) => {
    console.log(`${req.method} ${req.url} - ${new Date().toISOString()}`);
    next();
  });
}

// Shopify authentication and webhook handling
app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shopify.redirectToShopifyOrAppRoot()
);
app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: PrivacyWebhookHandlers })
);

// API routes
app.use("/api/webhooks", WebhookRouter);
app.use("/kids", KidsRouter);
app.use("/api/store-credit", StoreCreditRouter);
app.use("/api/newsletter", NewsletterRouter);

// Health check endpoint
app.get("/api/healthcheck", async (_req, res) => {
  console.log("Health check triggered");
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development"
  });
});



// Helper function to get current URL
const getCurrentUrl = (req) => {
  const host = req.get('host');
  return host?.includes('trycloudflare.com')
    ? `https://${host}`
    : `${req.protocol}://${host}`;
};

// Swagger test route
app.get("/api/docs/test", (req, res) => {
  const currentUrl = getCurrentUrl(req);
  res.json({
    message: "Swagger test route working",
    swaggerSpec: !!swaggerSpec,
    pathsCount: swaggerSpec?.paths ? Object.keys(swaggerSpec.paths).length : 0,
    availablePaths: swaggerSpec?.paths ? Object.keys(swaggerSpec.paths) : [],
    currentServerUrl: currentUrl,
    swaggerUrl: `${currentUrl}/api/docs`,
    timestamp: new Date().toISOString()
  });
});

// Additional routes
app.use("/index", indexRouter);

// Shopify authenticated routes
// app.use("/api/*", shopify.validateAuthenticatedSession());

app.get("/api/products/count", async (_req, res) => {
  const client = new shopify.api.clients.Graphql({
    session: res.locals.shopify.session,
  });

  const countData = await client.request(`
    query shopifyProductCount {
      productsCount {
        count
      }
    }
  `);

  res.status(200).send({ count: countData.data.productsCount.count });
});

app.post("/api/products", async (_req, res) => {
  let status = 200;
  let error = null;

  try {
    await productCreator(res.locals.shopify.session);
  } catch (e) {
    console.log(`Failed to process products/create: ${e.message}`);
    status = 500;
    error = e.message;
  }
  res.status(status).send({ success: status === 200, error });
});

// Swagger API Documentation setup
const setupSwagger = () => {
  try {
    // Determine which spec to use (fallback chain)
    let specToUse = swaggerSpec;
    if (!swaggerSpec?.paths || Object.keys(swaggerSpec.paths).length === 0) {
      specToUse = consolidatedSwaggerSpec;
    }
    if (!specToUse?.paths || Object.keys(specToUse.paths).length === 0) {
      specToUse = simpleSwaggerSpec;
    }

    app.use('/api/docs', swaggerUi.serve);
    app.get('/api/docs', (req, res, next) => {
      const currentUrl = getCurrentUrl(req);

      const dynamicSpec = {
        ...specToUse,
        servers: [
          { url: currentUrl, description: 'Current server' },
          { url: 'http://localhost:3000', description: 'Local development server' }
        ]
      };

      swaggerUi.setup(dynamicSpec, {
        explorer: true,
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: "MiniKlub Store Credit API"
      })(req, res, next);
    });
  } catch (error) {
    console.error('Error setting up Swagger UI:', error);
    // Fallback setup
    app.use('/api/docs', swaggerUi.serve);
    app.get('/api/docs', swaggerUi.setup(consolidatedSwaggerSpec));
  }
};

setupSwagger();

// Static file serving and CSP headers
app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));

// Shopify app frontend (commented out for API-only mode)
// app.use("/*", shopify.ensureInstalledOnShop(), async (_req, res, _next) => {
//   return res
//     .status(200)
//     .set("Content-Type", "text/html")
//     .send(
//       readFileSync(join(STATIC_PATH, "index.html"))
//         .toString()
//         .replace("%VITE_SHOPIFY_API_KEY%", process.env.SHOPIFY_API_KEY || "")
//     );
// });

// Global error handler (must be last)
app.use(globalErrorHandler);

// Start server
app.listen(PORT, () => {
  console.log(` Server running on port ${PORT}`);
  console.log(` API Documentation: http://localhost:${PORT}/api/docs`);
  console.log(` Health Check: http://localhost:${PORT}/api/healthcheck`);
  console.log(` Environment: ${process.env.NODE_ENV || "development"}`);
});

