{"name": "shopify-app-template-node", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"debug": "node --inspect-brk index.js", "dev": "cross-env NODE_ENV=development nodemon index.js --ignore ./frontend", "serve": "cross-env NODE_ENV=production node index.js"}, "type": "module", "engines": {"node": ">=16.13.0"}, "dependencies": {"@shopify/shopify-app-express": "^5.0.8", "@shopify/shopify-app-session-storage-sqlite": "^4.0.8", "axios": "^1.6.7", "compression": "^1.7.4", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "express": "^4.18.2", "joi": "^17.13.3", "multer": "^2.0.1", "serve-static": "^1.14.1", "zod": "^3.22.4"}, "devDependencies": {"nodemon": "^2.0.15", "prettier": "^2.6.2", "pretty-quick": "^3.1.3"}}