export const GET_CUSTOMER_BY_EMAIL = `
  query getCustomerByEmail($email: String!) {
    customers(query: $email, first: 1) {
      edges {
        node {
          id
          firstName
          lastName
          email
          emailMarketingConsent {
            marketingState
            marketingOptInLevel
          }
        }
      }
    }
  }
`;

export const CREATE_CUSTOMER = `
  mutation createCustomer($input: CustomerInput!) {
    customerCreate(input: $input) {
      customer {
        id
        email
        firstName
        lastName
        emailMarketingConsent {
          marketingState
          marketingOptInLevel
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const UPDATE_CUSTOMER_EMAIL_MARKETING = `
  mutation customerEmailMarketingConsentUpdate($input: CustomerEmailMarketingConsentUpdateInput!) {
    customerEmailMarketingConsentUpdate(input: $input) {
      customer {
        id
        email
        emailMarketingConsent {
          marketingState
          marketingOptInLevel
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

