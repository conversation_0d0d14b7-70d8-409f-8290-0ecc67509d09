// swagger.js
import swaggerJSDoc from 'swagger-jsdoc';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'MiniKlub Store Credit App API',
    version: '1.0.0',
    description: 'API for managing store credits, gift cards, and newsletter subscriptions',
    contact: {
      name: 'MiniKlub Development Team',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: process.env.SHOPIFY_APP_URL || 'https://hollow-celebration-dsc-excuse.trycloudflare.com',
      description: 'Development server'
    },
    {
      url: 'http://localhost:3000',
      description: 'Local development server'
    }
  ]
};

const options = {
  swaggerDefinition,
  apis: [
    'web/swagger.js',                        // This file for basic docs
    'web/docs/swagger-definitions.js',       // Schema definitions
    'web/docs/newsletterRouter.swagger.js',  // Newsletter API docs
    'web/docs/storeCreditRouter.swagger.js', // Store Credit API docs
    'web/routes/*.js'                        // Route files for any inline docs
  ],
};

/**
 * @swagger
 * /api/healthcheck:
 *   get:
 *     tags: [System]
 *     summary: Health check endpoint
 *     description: Returns the health status of the API
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "HealthCheck - OK with EB!!!"

 */

console.log('Swagger options:', options);

const swaggerSpec = swaggerJSDoc(options);

console.log('Generated swagger spec paths:', swaggerSpec?.paths ? Object.keys(swaggerSpec.paths) : 'No paths found');
console.log('Generated swagger spec components:', swaggerSpec?.components ? Object.keys(swaggerSpec.components) : 'No components found');

export default swaggerSpec;
