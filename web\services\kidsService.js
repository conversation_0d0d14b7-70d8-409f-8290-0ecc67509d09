import AppError from "../middlewares/AppError.js";
import {
  UPDATE_KIDS_INFO_MUTATION,
  GET_KIDS_INFO_QUERY,
  DELETE_KIDS_INFO_MUTATION,
} from "../utils/graphQlQueries.js";
import { uploadImage } from "../utils/shopifyImageUpload.js";
import shopify from "../shopify.js";

export const getChildProfilesService = async (
  shop,
  accessToken,
  customerId
) => {
  try {
    const { data } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken },
    }).request(GET_KIDS_INFO_QUERY, {
      variables: {
        customerId: `gid://shopify/Customer/${customerId}`,
      },
    });

    if (!data.customer) {
      throw new AppError("Customer not found", 404, {
        errors: [{ field: "customerId", message: "Customer not found" }],
      });
    }

    const profiles = data?.customer?.metafields?.edges
      .map(({ node }) => {
        try {
          const [name, dob, relationship, image_url] = JSON.parse(node.value);
          return {
            id: node.key,
            name,
            dob,
            relationship,
            image_url,
          };
        } catch (parseError) {
          console.error(
            `Error parsing metafield value for key ${node.key}:`,
            node.value
          );
          return null;
        }
      })
      .filter(Boolean); // Remove any null entries from failed parsing

    return { success: true, data: profiles };
  } catch (error) {
    if (error.body?.errors?.graphQLErrors) {
      throw new AppError("Shopify API Error", 400, {
        errors: error.body.errors.graphQLErrors.map((err) => ({
          field: "shopify",
          message: err.message,
          code: err.extensions?.code,
        })),
      });
    }
    throw error;
  }
};

export const updateChildProfilesService = async (
  shop,
  accessToken,
  data,
  files
) => {
  try {
    const { customerId, profiles } = data;

    const allIds = new Set([
      "child_1",
      "child_2",
      "child_3",
      "child_4",
      "child_5",
    ]);

    const updatedProfiles = await Promise.all(
      profiles.map(async (profile) => {
        allIds.delete(profile.id);

        // Upload image if provided
        const imageKey = `image_${profile.id}`;
        const imageFile = files[imageKey]?.[0];

        const image_url = await uploadImage(shop, accessToken, imageFile);

        return {
          ...profile,
          image_url,
        };
      })
    );

    const metafields = updatedProfiles.map((profile) => ({
      ownerId: `gid://shopify/Customer/${customerId}`,
      namespace: "custom",
      key: profile.id,
      value: JSON.stringify([
        profile.name,
        profile.dob,
        profile.relationship,
        profile.image_url,
      ]),
      type: "list.single_line_text_field",
    }));

    const { data: metafieldData } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken },
    }).request(UPDATE_KIDS_INFO_MUTATION, {
      variables: { metafields },
    });

    if (metafieldData.metafieldsSet.userErrors?.length > 0) {
      const uniqueErrors = metafieldData.metafieldsSet.userErrors.reduce(
        (acc, err) => {
          const key = `${err.message}-${err.code}`;
          if (!acc[key]) {
            acc[key] = {
              field: err.field?.[0] || "shopify",
              message: err.message,
              code: err.code,
            };
          }
          return acc;
        },
        {}
      );

      throw new AppError("Failed to update metafields", 400, {
        errors: Object.values(uniqueErrors),
      });
    }

    const deletingMetaFields = [...allIds].map((id) => ({
      ownerId: `gid://shopify/Customer/${customerId}`,
      namespace: "custom",
      key: id,
    }));

    await new shopify.api.clients.Graphql({
      session: { shop, accessToken },
    }).request(DELETE_KIDS_INFO_MUTATION, {
      variables: { metafields: deletingMetaFields },
    });

    return { success: true, data: updatedProfiles };
  } catch (error) {
    console.error("Error in updateChildProfilesService:", {
      message: error.message,
      stack: error.stack,
      response: error.response?.data,
      body: error.body,
    });

    if (error instanceof AppError) throw error;

    if (error.body?.errors?.graphQLErrors) {
      throw new AppError("Shopify API Error", 400, {
        errors: error.body.errors.graphQLErrors.map((err) => ({
          field: "shopify",
          message: err.message,
          code: err.extensions?.code,
        })),
      });
    }

    throw error;
  }
};
