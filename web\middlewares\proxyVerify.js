import crypto from "crypto";
export const verifyProxy = async function (req, res, next) {
  console.log("verify middlware start");
  if (req.query.shop) {
    if (!req.query.signature) {
      return res.sendStatus(403);
    }
    //console.log(ctx.query);
    const signature = req.query.signature;
    const sharedSecret = process.env.SHOPIFY_API_SECRET;
    // console.log("sharedSecret", sharedSecret);
    const def = req.query;
    delete def.signature;
    const sortedQuery = Object.keys(def)
      .map((key) => `${key}=${Array(def[key]).join(",")}`)
      .sort()
      .join("");
    // console.log("sortedQuery",sortedQuery);
    const calculatedSignature = crypto
      .createHmac("sha256", sharedSecret)
      .update(sortedQuery)
      .digest("hex");
    // console.log('***********************************8 calculatedSignature',calculatedSignature)
    // console.log('***********************************8 signature', signature)
    // const isOriginatedFromBata = (ctx.request.header['origin'] == 'https://www.batabd.com')
    // console.log('***********************************8 isOriginatedFromBata', isOriginatedFromBata)
    if (calculatedSignature === signature) {
      console.log("validated");
      return next();
    }
    console.log("UnAuthorised Request");
    return res.sendStatus(403);
  } else {
    console.log("No Proxy Query Was Received");
    return res.sendStatus(403);
  }
};
