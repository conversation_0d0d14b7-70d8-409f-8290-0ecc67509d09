class AppSuccess {
  constructor(
    res,
    result = {}
  ) {
    // If result is a plain object with no 'data' property, treat it as the data
    let {
      message = "Success",
      data,
      statusCode = 200,
      responseCode = 0,
      status = "success",
      ...rest
    } = result;

    // If 'data' is undefined, use the whole result as data
    if (typeof data === 'undefined') {
      data = { ...rest };
    }

    return res.status(statusCode).json({
      responseCode,
      status,
      message,
      data,
    });
  }
}

export default AppSuccess;
