/**
 * @fileoverview API Documentation Index
 * 
 * This file serves as the main entry point for all API documentation.
 * It imports and organizes all Swagger documentation files.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Import all documentation files
import './swagger-definitions.js';
import './storeCreditRouter.swagger.js';
import './newsletterRouter.swagger.js';

/**
 * @swagger
 * info:
 *   title: MiniKlub Store Credit App API
 *   version: 1.0.0
 *   description: |
 *     # Welcome to MiniKlub Store Credit App API
 *     
 *     This comprehensive API documentation covers all endpoints for managing store credits, 
 *     gift cards, and newsletter subscriptions in the MiniKlub e-commerce platform.
 *     
 *     ## Quick Navigation
 *     
 *     ### 🎁 Store Credit Management
 *     - **QwikCilver Gift Cards**: Authentication, balance checking, and redemption
 *     - **Rewardify Store Credit**: Customer account crediting and management
 *     
 *     ### 📧 Newsletter Management
 *     - **Subscription Management**: Subscribe and unsubscribe customers
 *     - **Email Marketing**: Manage customer email preferences
 *     
 *     ## Getting Started
 *     
 *     1. **Authentication**: Start with the appropriate auth endpoint for your use case
 *     2. **Test Endpoints**: Use the "Try it out" feature in each endpoint section
 *     3. **Integration**: Follow the examples in each endpoint documentation
 *     
 *     ## API Features
 *     
 *     - ✅ **Standardized Responses**: All endpoints return consistent response formats
 *     - ✅ **Comprehensive Error Handling**: Detailed error messages and codes
 *     - ✅ **Security**: Bearer token authentication for all protected endpoints
 *     - ✅ **Validation**: Input validation with clear error messages
 *     - ✅ **Documentation**: Interactive documentation with examples
 *     
 *     ## Support
 *     
 *     - 📖 **Documentation**: This interactive documentation
 *     - 📧 **Email**: <EMAIL>
 *     - 🔧 **Issues**: Report through your development team
 *     
 *     ---
 *     
 *     **Note**: This API is designed for internal use within the MiniKlub ecosystem. 
 *     Please ensure you have proper authorization before accessing any endpoints.
 */

export default {
  title: 'MiniKlub Store Credit App API Documentation',
  version: '1.0.0',
  description: 'Comprehensive API documentation for store credit and newsletter management',
  endpoints: {
    storeCredit: {
      auth: '/api/store-credit/auth',
      balance: '/api/store-credit/balance',
      redeem: '/api/store-credit/redeem-giftcard',
      rewardifyAuth: '/api/store-credit/rewardify-auth',
      rewardifyCredit: '/api/store-credit/rewardify-credit/{customerId}'
    },
    newsletter: {
      subscribe: '/api/newsletter/subscribe'
    }
  },
  tags: [
    'Store Credit',
    'QwikCilver',
    'Rewardify',
    'Newsletter',
    'Email Marketing'
  ]
};
